<?php


include('../../assets/admin_authorize.php');

// Database connection settings
// Include the extractedconfig.php which has the values from config.php
$config = include 'extractedconfig.php'; 

// Use the values from the config array
$host = $config['host'];
$dbname = $config['dbname'];
$username = $config['username'];
$password = $config['password'];

// Establish database connection
try {
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname`");
    $pdo->exec("USE `$dbname`");

    $pdo->exec(
        "CREATE TABLE IF NOT EXISTS botactivity_logs (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            ip VARCHAR(45) NOT NULL,
            reason TEXT NOT NULL,
            fullrefererUrl TEXT NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            userAgent TEXT NOT NULL,
            referer TEXT NOT NULL
        )"
    );
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

function getIpDetails($ip) {
    $url = "https://api.ipapi.is/?q=$ip";
    $response = file_get_contents($url);
    $ipDetails = json_decode($response, true);

    if ($ipDetails === null) {
        error_log("Error: Failed to fetch IP details for IP $ip");
        return null;
    }

    return $ipDetails;
}




// Function to ensure the IP details are updated correctly
function updateIpDetails($pdo, $ip) {
    // Fetch the current IP details from the external API
    $ipDetails = getIpDetails($ip);

    if ($ipDetails !== null) {
        // Check if the current record's IP details are up-to-date
        $stmt = $pdo->prepare("SELECT ip_details FROM botactivity_logs WHERE ip = :ip");
        $stmt->bindParam(':ip', $ip);
        $stmt->execute();
        $existingLog = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingLog) {
            $existingIpDetails = json_decode($existingLog['ip_details'], true);
            
            // Only update if the details don't match the current ones
            if ($existingIpDetails !== $ipDetails) {
                $ipDetailsJson = json_encode($ipDetails);

                // Update the IP details in the database
                $stmt = $pdo->prepare("UPDATE botactivity_logs SET ip_details = :ip_details WHERE ip = :ip");
                $stmt->bindParam(':ip', $ip);
                $stmt->bindParam(':ip_details', $ipDetailsJson);

                if ($stmt->execute()) {
                    echo "Updated IP details for IP $ip successfully.\n";
                } else {
                    error_log("Failed to update IP details for IP $ip");
                }
            }
        }
    }
}





// Check if data is posted via POST (both form data and JSON)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $inputData = null;

    // Check if the content type is JSON
    if (strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
        // Handle JSON request
        $inputData = json_decode(file_get_contents('php://input'), true);
    }
    
    // Check if the content type is form data
    if (strpos($_SERVER['CONTENT_TYPE'], 'application/x-www-form-urlencoded') !== false || 
        strpos($_SERVER['CONTENT_TYPE'], 'multipart/form-data') !== false) {
        // Handle regular form data
        $inputData = $_POST;
    }

    // Process the data if available
    if ($inputData) {
        // Extract data from the received input
        $ip = isset($inputData['ip']) ? $inputData['ip'] : null;
        $reason = isset($inputData['reason']) ? $inputData['reason'] : null;
        $fullrefererUrl = isset($inputData['fullrefererUrl']) ? $inputData['fullrefererUrl'] : null;
        $userAgent = isset($inputData['userAgent']) ? $inputData['userAgent'] : null;
        $referer = isset($inputData['referer']) ? $inputData['referer'] : null;

        if ($ip && $reason && $fullrefererUrl && $userAgent && $referer) {
            // Fetch IP details from the API
            $ipDetails = getIpDetails($ip);

            if ($ipDetails !== null) {
                $ipDetailsJson = json_encode($ipDetails);

                // Check if the IP already exists in the database
                $stmt = $pdo->prepare("SELECT id FROM botactivity_logs WHERE ip = :ip");
                $stmt->bindParam(':ip', $ip);
                $stmt->execute();
                $existingLog = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($existingLog) {
                    // Update the existing log
                    $stmt = $pdo->prepare(
                        "UPDATE botactivity_logs 
                         SET reason = :reason, fullrefererUrl = :fullrefererUrl, ip_details = :ip_details,
                             userAgent = :userAgent, referer = :referer
                         WHERE ip = :ip"
                    );
                    $stmt->bindParam(':ip', $ip);
                    $stmt->bindParam(':reason', $reason);
                    $stmt->bindParam(':fullrefererUrl', $fullrefererUrl);
                    $stmt->bindParam(':ip_details', $ipDetailsJson);
                    $stmt->bindParam(':userAgent', $userAgent);
                    $stmt->bindParam(':referer', $referer);
                    if ($stmt->execute()) {
                        echo "Log updated successfully.";
                    } else {
                        echo "Failed to update log.";
                    }
                } else {
                    // Insert a new log if the IP doesn't exist
                    $stmt = $pdo->prepare(
                        "INSERT INTO botactivity_logs (ip, reason, fullrefererUrl, ip_details, userAgent, referer) 
                         VALUES (:ip, :reason, :fullrefererUrl, :ip_details, :userAgent, :referer)"
                    );
                    $stmt->bindParam(':ip', $ip);
                    $stmt->bindParam(':reason', $reason);
                    $stmt->bindParam(':fullrefererUrl', $fullrefererUrl);
                    $stmt->bindParam(':ip_details', $ipDetailsJson);
                    $stmt->bindParam(':userAgent', $userAgent);
                    $stmt->bindParam(':referer', $referer);

                    if ($stmt->execute()) {
                        echo "Log added successfully.";
                    } else {
                        echo "Failed to add log.";
                    }
                }
            } else {
                echo "Failed to fetch IP details.";
            }
        } else {
            
        }
    }
}

// Function to fetch logs, optionally filtered by date
function fetchLogs($pdo, $dateFilter = '') {
    $query = "SELECT * FROM botactivity_logs";
    
    // If a date filter is provided, modify the query
    if ($dateFilter) {
        $query .= " WHERE DATE(timestamp) = :date";
    }

    $stmt = $pdo->prepare($query);

    // If a date filter is applied, bind the parameter
    if ($dateFilter) {
        $stmt->bindParam(':date', $dateFilter);
    }

    $stmt->execute();

    // Fetch all logs and return as an array
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Check if the date is provided in POST
$dateFilter = isset($_POST['date']) ? $_POST['date'] : '';

// Fetch logs based on the date filter
$logs = fetchLogs($pdo, $dateFilter);




?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anti Bot Activity Logs</title>
     <style>
     
       
        /* Button Styles */
        #dashboardButton {
            background-color: #4CAF50; /* Green */
            color: white;
            border: none;
            padding: 15px 32px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 8px;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Hover Effect */
        #dashboardButton:hover {
            background-color: #45a049;
            transform: scale(1.05);
        }

        /* Focus Effect */
        #dashboardButton:focus {
            outline: none;
        }

        /* Optional: Add shadow when pressed */
        #dashboardButton:active {
            background-color: #3e8e41;
            transform: scale(1);
        }
        
        
        .dashboardparent-container {
  display: flex;
  justify-content: center;  /* Centers the button horizontally */
  align-items: center;      /* Centers the button vertically */
  
}
    </style>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            color: #333;
        }
        header {
            background-color: black;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .container {
            padding: 20px;
            max-width: 1200px;
            margin: 20px auto;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        h2 {
            color: #333;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: #fff;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        td {
            background-color: #f9f9f9;
        }
        tr:nth-child(even) td {
            background-color: #f1f1f1;
        }
        input[type="date"] {
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        input[type="submit"]:hover {
            background-color: #45a049;
        }
        label {
            font-weight: bold;
        }
        .details {
            display: none; /* Initially hidden */
            padding: 10px;
            background-color: #f1f1f1;
            margin-top: 10px;
            border-radius: 4px;
        }
        .details ul {
            padding-left: 20px;
        }
        .details ul li {
            margin-bottom: 5px;
        }
        /* Responsive Design */
        @media (max-width: 768px) {
            table, th, td {
                font-size: 12px;
                padding: 8px;
            }
            input[type="date"], input[type="submit"] {
                font-size: 14px;
            }
        }
    </style>
    
    
</head>
<body>

<header>
    <h1>Anti Bot Activity Logs</h1>
</header>


  
   <div class="container">
    <div class="dashboardparent-container">
        <button id="dashboardButton">Dashboard</button>
    </div>
    
    <form method="POST">
        <label for="date">Filter by Date:</label>
        <input type="date" id="date" name="date" value="<?php echo isset($_POST['date']) ? $_POST['date'] : ''; ?>">
        <input type="submit" value="Filter">
    </form>

    <h2>Activity Logs Dashboard</h2>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>IP Address</th>
                <th>Reason</th>
                <th>Full Referrer URL</th>
                <th>Timestamp</th>
                <th>User Agent</th>
                <th>Referer</th>
                <th>IP Details</th>
            </tr>
        </thead>
       <tbody>
    <tbody>
    <?php
        // Reverse the logs array to show the most recent first
        $logs = array_reverse($logs);
    ?>
    <?php foreach ($logs as $log): ?>
        <?php 
            // Check if the fullrefererUrl contains ".dev"
            if (strpos($log['fullrefererUrl'], '.dev') !== false) {
                continue; // Skip this iteration if the URL contains ".dev"
            }
        ?>
        <tr class="log-row" data-id="<?php echo htmlspecialchars($log['id']); ?>">
            <td><?php echo htmlspecialchars($log['id']); ?></td>
            <td><?php echo htmlspecialchars($log['ip']); ?></td>
            <td><?php echo htmlspecialchars($log['reason']); ?></td>
            <td><?php echo htmlspecialchars($log['fullrefererUrl']); ?></td>
            <td><?php echo htmlspecialchars($log['timestamp']); ?></td>
            <td><?php echo htmlspecialchars($log['userAgent']); ?></td>
            <td><?php echo htmlspecialchars($log['referer']); ?></td>
            <td>
                <button class="toggle-details">Show Details</button>
            </td>
        </tr>
        <tr id="details-<?php echo htmlspecialchars($log['id']); ?>" class="details">
            <td colspan="8">
                <?php 
                    // Decode the JSON stored in 'ip_details' column
                    $ipDetails = json_decode($log['ip_details'], true);
                    if ($ipDetails && is_array($ipDetails)): 
                ?>
                    <ul>
                        <?php foreach ($ipDetails as $key => $value): ?>
                        <?php if (is_array($value)): ?>
                            <strong><?php echo ucfirst($key); ?>:</strong>
                            <ul>
                                <?php foreach ($value as $subKey => $subValue): ?>
                                    <li><strong><?php echo ucfirst($subKey); ?>:</strong> <?php echo htmlspecialchars($subValue); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <strong><?php echo ucfirst($key); ?>:</strong> <?php echo htmlspecialchars($value); ?><br>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <span>No IP details available</span>
                <?php endif; ?>
            </td>
        </tr>
    <?php endforeach; ?>
</tbody>




<script>
    document.querySelectorAll('.toggle-details').forEach(button => {
        button.addEventListener('click', function() {
            const rowId = this.closest('tr').dataset.id;
            const detailsRow = document.getElementById('details-' + rowId);

            if (detailsRow.style.display === 'none' || detailsRow.style.display === '') {
                detailsRow.style.display = 'table-row';
                this.textContent = 'Hide Details';
            } else {
                detailsRow.style.display = 'none';
                this.textContent = 'Show Details';
            }
        });
    });
</script>



    <script>
        document.getElementById("dashboardButton").addEventListener("click", function() {
            // Create a new XMLHttpRequest object
            var xhr = new XMLHttpRequest();

            // Define the path to redirect to (e.g., '/dashboard')
            var path = '../dashboard.php'; // Adjust path if needed

            // Open a GET request
            xhr.open("GET", path, true);

            // Set up a callback for the response
            xhr.onload = function() {
                if (xhr.status === 200) {
                    // On success, redirect using JavaScript
                    window.location.href = path;
                } else {
                    console.error("Failed to load the dashboard.");
                }
            };

            // Send the request
            xhr.send();
        });
    </script>
    
</body>
</html>